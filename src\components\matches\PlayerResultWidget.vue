<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useMatchesService } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { Badge } from '@/components/ui/badge'
import { Medal } from 'lucide-vue-next'
import type { Match, MatchResult } from '@/api/feathers-client'

const props = defineProps<{
  match: Match | null | undefined
  compact?: boolean // For smaller display in MatchDetailsWidget
}>()

const matchesService = useMatchesService()
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

const playerResult = ref<MatchResult | null>(null)
const isLoadingResult = ref(false)

// Check if match is in the past
const isMatchInPast = computed(() => {
  if (!props.match?.endDate) return false
  return new Date(props.match.endDate) < new Date()
})

async function fetchPlayerResult() {
  if (!props.match?.id || !activePlayer.value) {
    playerResult.value = null
    return
  }

  // Only check for results if match is in the past
  if (!isMatchInPast.value) {
    playerResult.value = null
    return
  }

  isLoadingResult.value = true
  try {
    const results = await matchesService.findMatchResults({
      query: {
        matchId: props.match.id,
        playerId: activePlayer.value.id,
        $populate: 'player'
      }
    })

    playerResult.value = results.find(
      result => result.matchId === props.match!.id && result.playerId === activePlayer.value!.id
    ) || null
  } catch (err) {
    console.error('Failed to fetch player result:', err)
    playerResult.value = null
  } finally {
    isLoadingResult.value = false
  }
}

// Watch for changes in match or activePlayer
watch([() => props.match, activePlayer], () => {
  fetchPlayerResult()
}, { immediate: true })

onMounted(() => {
  fetchPlayerResult()
})

// Get medal display info for places 1-3
const medalInfo = computed(() => {
  if (!playerResult.value?.place) return null

  switch (playerResult.value.place) {
    case 1:
      return { class: 'text-yellow-500', size: 'h-8 w-8' } // Gold
    case 2:
      return { class: 'text-gray-400', size: 'h-8 w-8' } // Silver
    case 3:
      return { class: 'text-amber-600', size: 'h-8 w-8' } // Bronze
    default:
      return null
  }
})
</script>

<template>
  <div v-if="playerResult && isMatchInPast" :class="[
    'p-3 bg-primary/10 border border-primary/20 rounded-lg',
    compact ? 'p-2' : 'p-4'
  ]">
    <div class="flex items-center gap-4">
      <!-- Medal for places 1-3 -->
      <div v-if="medalInfo" class="flex-shrink-0">
        <Medal :class="[medalInfo.class, medalInfo.size]" />
      </div>

      <div class="flex items-center justify-between flex-1">
        <div>
          <p :class="[
            'font-semibold text-primary mb-2',
            compact ? 'text-sm mb-1' : ''
          ]">Your Result</p>
          <div class="flex gap-1">
            <Badge
              v-if="playerResult.styleDivision"
              variant="default"
              :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
            >
              {{ playerResult.styleDivision }}
            </Badge>
            <Badge
              v-if="playerResult.ageDivision"
              variant="secondary"
              :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
            >
              {{ playerResult.ageDivision }}
            </Badge>
            <Badge
              v-if="playerResult.genderDivision"
              variant="outline"
              :class="compact ? 'text-xs px-1 py-0' : 'text-xs'"
            >
              {{ playerResult.genderDivision }}
            </Badge>
          </div>
        </div>
        <div class="text-right">
          <p :class="[
            'font-bold text-primary',
            compact ? 'text-lg' : 'text-xl'
          ]">{{ playerResult.points }}/{{ playerResult.maxPoints }}</p>
          <p v-if="playerResult.place" :class="[
            'text-muted-foreground',
            compact ? 'text-xs' : 'text-sm'
          ]">Place: {{ playerResult.place }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
