import type { Player } from '@/api/feathers-client'
import { api } from '@/api/feathers-client'
import type { Params } from '@feathersjs/feathers'

// Service-only players store - no global state, only API methods
export const usePlayersService = () => {
  // Use the typed service from the api client
  const playersService = api.players

  // Player API methods - no state management, just API calls
  async function findPlayers(params?: Params) {
    try {
      const result = await playersService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as Player[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch players.')
      }
    }
  }

  async function getPlayer(id: number) {
    try {
      const result = await playersService.get(id)
      return result
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to fetch player with id ${id}.`)
      }
    }
  }

  async function searchPlayers(query: string, limit = 10) {
    try {
      // Use lastname query parameter for server-side filtering
      const result = await playersService.find({
        query: {
          lastname: query,
          isActive: true,
          $limit: limit,
          $sort: { lastname: 1, firstname: 1 }
        }
      })

      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as Player[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to search players.')
      }
    }
  }

  return {
    findPlayers,
    getPlayer,
    searchPlayers
  }
}
