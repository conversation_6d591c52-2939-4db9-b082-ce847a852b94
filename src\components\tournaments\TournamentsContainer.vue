<script setup lang="ts">
import { Input } from '@/components/ui/input'
import { refDebounced, useWindowSize } from '@vueuse/core'
import {
  Search,
  Filter,
  MapPin,
  ChevronsUpDown,
  Calendar as CalendarIcon
} from 'lucide-vue-next'
import { computed, ref, onMounted } from 'vue'
import dayjs from '@/lib/dayjs'
import { useUserStore } from '@/stores/user'
import { useFiltersStore } from '@/stores/filters'
import { storeToRefs } from 'pinia'
import TournamentsList from './TournamentsList.vue'
import TournamentDetailsWidget from './TournamentDetailsWidget.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import ArcheryMap from '@/components/map/ArcheryMap.vue'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import type { Match } from '@/api/feathers-client'
import type { ExtendedMatch, TournamentWithMatches } from '@/types'
import { useTournamentsService } from '@/stores/tournaments'
import { useI18n } from 'vue-i18n'

interface TournamentsProps {
  defaultLayout?: number[]
  navCollapsedSize?: number
}

const props = withDefaults(defineProps<TournamentsProps>(), {
  defaultLayout: () => [265, 655],
  navCollapsedSize: 44
})

const { t } = useI18n()
const { width } = useWindowSize()
const tournamentsService = useTournamentsService()
const userStore = useUserStore()
const filtersStore = useFiltersStore()
const { activePlayer } = storeToRefs(userStore)
const { selectedFederations } = storeToRefs(filtersStore)

// Local state management
const tournaments = ref<TournamentWithMatches[]>([])
const currentTournament = ref<TournamentWithMatches | null>(null)
const hoveredTournaments = ref<TournamentWithMatches[]>([])
const hoveredTournamentId = ref<number | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)

// Computed matches for the selected tournament (from tournament.matches)
const tournamentMatches = computed(() => {
  if (!currentTournament.value?.matches) return []
  return currentTournament.value.matches
})

// Search state
const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)

// Filter state
const showFilters = ref(true)
const filterThisWeek = ref(false)
const filterNextMonth = ref(false)
const filterNext3Months = ref(false)
const filterNext6Months = ref(false)
const filterThisYear = ref(false)
const filterNearMe = ref(false)
const nearMeDistance = ref([50]) // Distance in kilometers, default 50km
const sortBy = ref<'distance' | 'date' | 'name'>('date')

// Filter toggle functions
const toggleFiltersVisibility = () => {
  showFilters.value = !showFilters.value
}

const toggleThisWeekFilter = () => {
  filterThisWeek.value = !filterThisWeek.value
}

const toggleNextMonthFilter = () => {
  filterNextMonth.value = !filterNextMonth.value
}

const toggleNext3MonthsFilter = () => {
  filterNext3Months.value = !filterNext3Months.value
}

const toggleNext6MonthsFilter = () => {
  filterNext6Months.value = !filterNext6Months.value
}

const toggleThisYearFilter = () => {
  filterThisYear.value = !filterThisYear.value
}

const toggleNearMeFilter = () => {
  filterNearMe.value = !filterNearMe.value
}

const sortTournaments = (criteria: 'distance' | 'date' | 'name') => {
  sortBy.value = criteria
}

// Helper function for distance calculation (Haversine)
function deg2rad(deg: number) {
  return deg * (Math.PI / 180)
}

// Sidebar styles for responsive design
const sidebarStyles = computed(() => {
  if (width.value >= 1024) {
    return { '--sidebar-width': '25rem' }
  } else if (width.value >= 768) {
    return { '--sidebar-width': '15rem' }
  } else {
    return { '--sidebar-width': '25rem' }
  }
})

// Fetch tournaments
const fetchTournaments = async () => {
  isLoading.value = true
  error.value = null
  try {
    const result = await tournamentsService.findTournaments({
      query: {
        $limit: 100,
        isActive: true,
        completedAt: null,
        $populate: ['organizer', 'federation', 'matches']
      }
    })
    if (Array.isArray(result)) {
      tournaments.value = result.map(t => ({ ...t, matches: t.matches as ExtendedMatch[] || [] })) as TournamentWithMatches[]
      console.log('Fetched tournaments:', tournaments.value)
    } else {
      console.error('Unexpected tournamentsService response:', result)
      tournaments.value = []
    }
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Failed to fetch tournaments')
    console.error('Failed to fetch tournaments:', err)
  } finally {
    isLoading.value = false
  }
}

// Search filtered tournaments
const searchFilteredTournaments = computed(() => {
  if (!debouncedSearchQuery.value) {
    return tournaments.value
  }

  const query = debouncedSearchQuery.value.toLowerCase()
  return tournaments.value.filter(tournament =>
    tournament.name?.toLowerCase().includes(query) ||
    tournament.description?.toLowerCase().includes(query) ||
    tournament.organizer?.name?.toLowerCase().includes(query)
  )
})

// Filtered tournaments with all filters applied
const filteredTournaments = computed(() => {
  let itemsToFilter = searchFilteredTournaments.value

  // Time-based filters (based on tournament start dates from first match)
  if (filterThisWeek.value) {
    const today = dayjs().startOf('day')
    const firstDayOfWeek = today.startOf('week')
    const lastDayOfWeek = today.endOf('week')

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches || tournament.matches.length === 0) return false
      const firstMatch = tournament.matches[0]
      if (!firstMatch.startDate) return false
      const matchStartDate = dayjs(firstMatch.startDate).startOf('day')
      return matchStartDate.isSameOrAfter(firstDayOfWeek) && matchStartDate.isSameOrBefore(lastDayOfWeek)
    })
  }

  if (filterNextMonth.value) {
    const today = dayjs().startOf('day')
    const nextMonthStart = today.add(1, 'month').startOf('month')
    const nextMonthEnd = today.add(1, 'month').endOf('month')

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches || tournament.matches.length === 0) return false
      const firstMatch = tournament.matches[0]
      if (!firstMatch.startDate) return false
      const matchStartDate = dayjs(firstMatch.startDate).startOf('day')
      return matchStartDate.isSameOrAfter(nextMonthStart) && matchStartDate.isSameOrBefore(nextMonthEnd)
    })
  }

  if (filterNext3Months.value) {
    const today = dayjs().startOf('day')
    const next3MonthsEnd = today.add(3, 'month').endOf('month')

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches || tournament.matches.length === 0) return false
      const firstMatch = tournament.matches[0]
      if (!firstMatch.startDate) return false
      const matchStartDate = dayjs(firstMatch.startDate).startOf('day')
      return matchStartDate.isSameOrAfter(today) && matchStartDate.isSameOrBefore(next3MonthsEnd)
    })
  }

  if (filterNext6Months.value) {
    const today = dayjs().startOf('day')
    const next6MonthsEnd = today.add(6, 'month').endOf('month')

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches || tournament.matches.length === 0) return false
      const firstMatch = tournament.matches[0]
      if (!firstMatch.startDate) return false
      const matchStartDate = dayjs(firstMatch.startDate).startOf('day')
      return matchStartDate.isSameOrAfter(today) && matchStartDate.isSameOrBefore(next6MonthsEnd)
    })
  }

  if (filterThisYear.value) {
    const today = dayjs().startOf('day')
    const startOfYear = today.startOf('year')
    const endOfYear = today.endOf('year')

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches || tournament.matches.length === 0) return false
      const firstMatch = tournament.matches[0]
      if (!firstMatch.startDate) return false
      const matchStartDate = dayjs(firstMatch.startDate).startOf('day')
      return matchStartDate.isSameOrAfter(startOfYear) && matchStartDate.isSameOrBefore(endOfYear)
    })
  }

  // "Near me" filter
  if (filterNearMe.value) {
    const playerLat = activePlayer.value?.latitude
    const playerLng = activePlayer.value?.longitude

    if (typeof playerLat === 'number' && typeof playerLng === 'number') {
      const nearRadiusKm = nearMeDistance.value[0]

      itemsToFilter = itemsToFilter.filter(tournament => {
        if (!tournament.matches || tournament.matches.length === 0) return false
        const firstMatch = tournament.matches[0]
        if (typeof firstMatch.latitude !== 'number' || typeof firstMatch.longitude !== 'number') return false

        const R = 6371 // Radius of the Earth in km
        const dLat = deg2rad(firstMatch.latitude - playerLat)
        const dLon = deg2rad(firstMatch.longitude - playerLng)
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(deg2rad(playerLat)) *
          Math.cos(deg2rad(firstMatch.latitude)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2)
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        const distanceKm = R * c
        return distanceKm <= nearRadiusKm
      })
    }
  }

  // Federation filter
  if (!filtersStore.isAllFederationsSelected) {
    itemsToFilter = itemsToFilter.filter(tournament => {
      if (tournament.federation?.name) {
        return selectedFederations.value.includes(tournament.federation.name)
      }
      return false
    })
  }

  // Sorting logic
  const sortedItems = [...itemsToFilter].sort((a, b) => {
    if (sortBy.value === 'date') {
      const aDate = a.matches?.[0]?.startDate ? dayjs(a.matches[0].startDate) : dayjs('9999-12-31')
      const bDate = b.matches?.[0]?.startDate ? dayjs(b.matches[0].startDate) : dayjs('9999-12-31')
      return aDate.diff(bDate)
    } else if (sortBy.value === 'name') {
      return (a.name || '').localeCompare(b.name || '')
    } else if (sortBy.value === 'distance') {
      const playerLat = activePlayer.value?.latitude
      const playerLng = activePlayer.value?.longitude
      if (typeof playerLat !== 'number' || typeof playerLng !== 'number') return 0

      const getDistance = (tournament: TournamentWithMatches) => {
        if (!tournament.matches || tournament.matches.length === 0) return Infinity
        const firstMatch = tournament.matches[0]
        if (typeof firstMatch.latitude !== 'number' || typeof firstMatch.longitude !== 'number') return Infinity
        return Math.sqrt(Math.pow(firstMatch.latitude - playerLat, 2) + Math.pow(firstMatch.longitude - playerLng, 2))
      }

      return getDistance(a) - getDistance(b)
    }
    return 0
  })

  return sortedItems
})

// Tournament selection handlers
const selectTournament = (id: number) => {
  if (currentTournament.value && currentTournament.value.id === id) {
    currentTournament.value = null // Deselect if the same tournament is clicked again
  } else {
    const tournament = tournaments.value.find(t => t.id === id)
    currentTournament.value = tournament || null
  }
}

const setHoveredTournamentId = (id: number | null) => {
  hoveredTournamentId.value = id
  if (id) {
    const tournament = tournaments.value.find(t => t.id === id)
    hoveredTournaments.value = tournament ? [tournament] : []
  } else {
    hoveredTournaments.value = []
  }
}

const handleSelectTournamentFromMap = (matchId: string) => {
  const id = parseInt(matchId, 10)
  // Find the tournament that contains this match
  for (const tournament of searchFilteredTournaments.value) {
    if (tournament.matches?.some((match: Match) => match.id === id)) {
      selectTournament(tournament.id)
      break
    }
  }
}

// Map coordinates - use selected tournament's first match coordinates if available
const mapLatitude = computed(() => {
  if (currentTournament.value?.matches && currentTournament.value.matches.length > 0) {
    const firstMatch = currentTournament.value.matches[0]
    return firstMatch.latitude || null
  }
  return null
})

const mapLongitude = computed(() => {
  if (currentTournament.value?.matches && currentTournament.value.matches.length > 0) {
    const firstMatch = currentTournament.value.matches[0]
    return firstMatch.longitude || null
  }
  return null
})

// All matches from tournaments for map display
const allMatches = computed(() => {
  const matches = []
  for (const tournament of searchFilteredTournaments.value) {
    if (tournament.matches) {
      matches.push(...tournament.matches)
    }
  }
  return matches
})

// Matches to highlight when hovering over a tournament
const hoveredMatches = computed(() => {
  if (hoveredTournamentId.value) {
    const tournament = searchFilteredTournaments.value.find(t => t.id === hoveredTournamentId.value)
    return tournament?.matches || []
  }
  return []
})

onMounted(() => {
  fetchTournaments()
})
</script>

<template>
  <SidebarProvider :style="sidebarStyles">
    <SidebarInset>
      <div class="flex flex-col gap-4">
        <div v-if="isLoading" class="text-center p-4">
          Loading tournaments...
        </div>
        <div v-else-if="error" class="text-center p-4 text-red-500">
          Error loading tournaments: {{ error?.message }}
        </div>
        <div v-else>
          <div class="h-full">
            <div class="flex items-center justify-between gap-2 px-4 py-2">
              <div class="flex items-center gap-2">
                <!-- Mobile sidebar trigger -->
                <SidebarTrigger class="md:hidden" />

                <div class="flex items-center gap-2">
                  <h2 class="text-lg font-semibold">{{ t('navigation.tournaments') }}</h2>
                  <Badge variant="outline">{{ filteredTournaments.length }}</Badge>
                </div>
              </div>
            </div>

            <!-- Search and filters -->
            <div class="bg-background/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div class="flex items-center gap-2">
                <div class="relative flex-1">
                  <Search class="absolute left-2 top-2.5 size-4 text-muted-foreground" />
                  <Input v-model="searchQuery" placeholder="Search tournaments..." class="pl-8" />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  :class="{ 'bg-primary text-primary': showFilters }"
                  @click="toggleFiltersVisibility"
                >
                  <Filter class="h-4 w-4" />
                  <span class="sr-only">{{ t('filters.toggleFilters') }}</span>
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="outline" class="flex items-center gap-1">
                      <span>Sort by: {{ sortBy }}</span>
                      <ChevronsUpDown class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem @click="sortTournaments('date')">Date</DropdownMenuItem>
                    <DropdownMenuItem @click="sortTournaments('distance')">Distance</DropdownMenuItem>
                    <DropdownMenuItem @click="sortTournaments('name')">Name</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div v-if="showFilters" class="mt-2 space-y-2">
                <!-- Time-based filters -->
                <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterThisWeek }"
                    @click="toggleThisWeekFilter"
                  >
                    <CalendarIcon class="h-3 w-3" />
                    <span>{{ t('filters.thisWeek') }}</span>
                  </Badge>

                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterNextMonth }"
                    @click="toggleNextMonthFilter"
                  >
                    <CalendarIcon class="h-3 w-3" />
                    <span>{{ t('filters.nextMonth') }}</span>
                  </Badge>

                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterNext3Months }"
                    @click="toggleNext3MonthsFilter"
                  >
                    <CalendarIcon class="h-3 w-3" />
                    <span>{{ t('filters.next3Months') }}</span>
                  </Badge>

                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterNext6Months }"
                    @click="toggleNext6MonthsFilter"
                  >
                    <CalendarIcon class="h-3 w-3" />
                    <span>{{ t('filters.next6Months') }}</span>
                  </Badge>

                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterThisYear }"
                    @click="toggleThisYearFilter"
                  >
                    <CalendarIcon class="h-3 w-3" />
                    <span>{{ t('filters.thisYear') }}</span>
                  </Badge>
                </div>

                <!-- Location filters -->
                <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                  <Badge
                    variant="outline"
                    class="flex items-center gap-1 cursor-pointer"
                    :class="{ 'border-primary text-primary': filterNearMe }"
                    @click="toggleNearMeFilter"
                  >
                    <MapPin class="h-3 w-3" />
                    <span>{{ t('filters.nearMe') }}</span>
                  </Badge>

                  <!-- Inline distance slider for "Near me" filter -->
                  <div v-if="filterNearMe" class="flex items-center gap-2 px-2 py-1 bg-muted/30 rounded-md min-w-0">
                    <span class="text-xs text-muted-foreground whitespace-nowrap">{{ nearMeDistance[0] }}km</span>
                    <Slider
                      v-model="nearMeDistance"
                      :min="5"
                      :max="500"
                      :step="5"
                      class="w-20"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="filteredTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </div>
          </div>
        </div>
      </div>
    </SidebarInset>
    <Sidebar
     class="sticky top-14 h-[calc(100svh-3.5rem)] border-l"
      collapsible="offcanvas"
    >
      <SidebarContent class="flex flex-col gap-0">
        <div v-if="currentTournament" class="mb-0 border-b border-sidebar-border pb-0">
          <TournamentDetailsWidget
            :tournament="currentTournament"
            :matches="tournamentMatches"
            @close="currentTournament = null"
          />
        </div>

        <div class="mb-0 border-b border-sidebar-border pb-0">
          <div class="h-52">
            <ArcheryMap
              :latitude="mapLatitude"
              :longitude="mapLongitude"
              :matches-to-display="allMatches"
              :hovered-matches="hoveredMatches"
              :hovered-match-id="null"
              @select-match="handleSelectTournamentFromMap"
            />
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
