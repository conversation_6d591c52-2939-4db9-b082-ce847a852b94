<script setup lang="ts">
import { Input } from '@/components/ui/input'
import { refDebounced, useWindowSize } from '@vueuse/core'
import {
  Search,
  Filter,
  MapPin,
  Users,
  ChevronsUpDown,
  Plus,
  Trophy,
  Calendar as CalendarIcon // Renamed to avoid conflict with EventCalendar component
} from 'lucide-vue-next'
import { computed, ref, onMounted, watch } from 'vue'
import MatchesList from './MatchesList.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { useMatchesService } from '@/stores/matches'
import { useAuthStore } from '@/stores/auth'; // Import auth store
import { useUserStore } from '@/stores/user' // Import user store
import { useFiltersStore } from '@/stores/filters' // Import filters store
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Imports moved from MatchesView.vue
import type { DateRange, DateValue } from 'reka-ui' // Assuming reka-ui is a globally available type or installed package
import EventCalendar from '@/components/EventCalendar.vue'
import ArcheryMap from '@/components/map/ArcheryMap.vue'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import type { Match } from '@/api/feathers-client'
import dayjs from '@/lib/dayjs'

interface MatchesProps {
  defaultLayout?: number[]
  navCollapsedSize?: number
  matchType?: 'search' | 'results' | 'archive' | 'watched'
}

const props = withDefaults(defineProps<MatchesProps>(), {
  defaultLayout: () => [265, 655],
  navCollapsedSize: 44,
  matchType: 'search'
})

const { t } = useI18n()
const router = useRouter()
const matchesService = useMatchesService()
const authStore = useAuthStore(); // Initialize auth store
const userStore = useUserStore()
const filtersStore = useFiltersStore()
// const { user } = storeToRefs(authStore); // Get user for "Near me" filter
const { activePlayer, activeOrganizer } = storeToRefs(userStore)
const { selectedFederations } = storeToRefs(filtersStore)

// Local state management
const matches = ref<Match[]>([])
const currentMatch = ref<Match | null>(null)
const hoveredMatchId = ref<number | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)

const searchValue = ref('')
const debouncedSearch = refDebounced(searchValue, 250)

// Local search filtering
const filteredMatches = computed(() => {
  const query = debouncedSearch.value.trim().toLowerCase()
  if (!query) {
    return matches.value
  }
  return matches.value.filter(match =>
    match.name.toLowerCase().includes(query) ||
    match.city?.toLowerCase().includes(query) ||
    match.country?.toLowerCase().includes(query)
  )
})

// Function to load matches based on match type
const loadMatches = async () => {
  isLoading.value = true
  error.value = null

  try {
    const query: any = {
      $limit: 100,
      isActive: true,
      $populateCount: 'match-registrations'
    }

    // Set date filter and sort based on match type
    if (props.matchType === 'results' || props.matchType === 'archive') {
      // For results and archive, show past matches
      query.startDate = { $lt: new Date().toLocaleDateString('en-CA') }
      query.$sort = { startDate: -1 } // Most recent first
    } else {
      // For search and watched, show future matches
      query.startDate = { $gt: new Date().toLocaleDateString('en-CA') }
      query.$sort = { startDate: 1 } // Earliest first
    }

    matches.value = await matchesService.findMatches({ query })
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Failed to load matches')
  } finally {
    isLoading.value = false
  }
}

// Load matches on mount
onMounted(() => {
  loadMatches()
})

// Function to reset all filters
const resetFilters = () => {
  filterThisWeek.value = false
  filterLastWeek.value = false
  filterNextMonth.value = false
  filterLastMonth.value = false
  filterNext3Months.value = false
  filterLast3Months.value = false
  filterNext6Months.value = false
  filterLast6Months.value = false
  filterThisYear.value = false
  filterNearMe.value = false
  filterAvailable.value = false
  filterTournament.value = false
  selectedCalendarDateRange.value = undefined
}

// Watch for changes in matchType and reload data
watch(() => props.matchType, () => {
  resetFilters()
  loadMatches()
}, { immediate: false })

// Match selection handlers
const selectMatch = (matchId: number) => {
  if (currentMatch.value && currentMatch.value.id === matchId) {
    currentMatch.value = null; // Unselect if the same match is clicked again
  } else {
    const foundMatch = matches.value.find(m => m.id === matchId);
    if (foundMatch) {
      currentMatch.value = foundMatch;
    } else {
      currentMatch.value = null;
    }
  }
}

const setHoveredMatchId = (id: number | null) => {
  hoveredMatchId.value = id;
}

const mapLatitude = computed(() => currentMatch.value?.latitude)
const mapLongitude = computed(() => currentMatch.value?.longitude)

const selectedCalendarDateRange = ref<DateRange | undefined>()
const hoveredMatches = ref<Match[]>([])

function handleCalendarDateRangeUpdate(value: DateRange | undefined) {
  selectedCalendarDateRange.value = value
}

function handleCalendarDateHover(matches: Match[]) {
  hoveredMatches.value = matches
}

function handleCalendarDateUnhover() {
  hoveredMatches.value = []
}

function handleCalendarSelectMatch(matchId: number) {
  selectMatch(matchId)
}

const calendarMatches = computed(() => matches.value as Match[] || [])

const convertDateValueToJSDate = (dateValue: DateValue | undefined | null): Date | null => {
  if (!dateValue) return null;
  // If dateValue is already a Date object, return it
  if (dateValue instanceof Date) {
    return dateValue;
  }
  // If dateValue has a toDate method (like some date picker objects), use it
  if (typeof (dateValue as any)?.toDate === 'function') {
    return (dateValue as any).toDate();
  }
  // If it's a string, try parsing with dayjs
  if (typeof dateValue === 'string') {
    const d = dayjs(dateValue);
    return d.isValid() ? d.toDate() : null;
  }
  // Fallback for other types that Date constructor might handle
  const d = new Date(dateValue as any);
  return isNaN(d.getTime()) ? null : d;
};

const matchesFilteredByCalendar = computed(() => {
  const baseMatches = filteredMatches.value;
  if (!selectedCalendarDateRange.value || (!selectedCalendarDateRange.value.start && !selectedCalendarDateRange.value.end)) {
    return baseMatches;
  }

  const { start, end } = selectedCalendarDateRange.value;

  return baseMatches.filter(match => {
    if (!match.startDate) return false;
    const matchDate = dayjs(match.startDate).startOf('day');

    const startDate = convertDateValueToJSDate(start);
    const endDate = convertDateValueToJSDate(end);

    const dayjsStartDate = startDate ? dayjs(startDate).startOf('day') : null;
    const dayjsEndDate = endDate ? dayjs(endDate).startOf('day') : null;

    if (dayjsStartDate && dayjsEndDate) {
      return matchDate.isSameOrAfter(dayjsStartDate) && matchDate.isSameOrBefore(dayjsEndDate);
    } else if (dayjsStartDate) {
      return matchDate.isSame(dayjsStartDate);
    }
    return true;
  });
});

const showFilters = ref(true);
// Dynamic filter refs based on match type
const filterThisWeek = ref(false);
const filterLastWeek = ref(false);
const filterNextMonth = ref(false);
const filterLastMonth = ref(false);
const filterNext3Months = ref(false);
const filterLast3Months = ref(false);
const filterNext6Months = ref(false);
const filterLast6Months = ref(false);
const filterThisYear = ref(false);
const filterNearMe = ref(false);
const filterAvailable = ref(false);
const filterTournament = ref(false);
const nearMeDistance = ref([50]); // Distance in kilometers, default 50km

const sortBy = ref<'distance' | 'date' | 'players'>('date');

const sortMatches = (criteria: 'distance' | 'date' | 'players') => {
  sortBy.value = criteria;
};

const toggleFiltersVisibility = () => {
  showFilters.value = !showFilters.value;
};

const setExclusiveTimeFilter = (filter: 'thisWeek' | 'lastWeek' | 'nextMonth' | 'lastMonth' | 'next3Months' | 'last3Months' | 'next6Months' | 'last6Months' | 'thisYear') => {
  filterThisWeek.value = filter === 'thisWeek';
  filterLastWeek.value = filter === 'lastWeek';
  filterNextMonth.value = filter === 'nextMonth';
  filterLastMonth.value = filter === 'lastMonth';
  filterNext3Months.value = filter === 'next3Months';
  filterLast3Months.value = filter === 'last3Months';
  filterNext6Months.value = filter === 'next6Months';
  filterLast6Months.value = filter === 'last6Months';
  filterThisYear.value = filter === 'thisYear';
};

const toggleThisWeekFilter = () => {
  if (!filterThisWeek.value) {
    setExclusiveTimeFilter('thisWeek');
  } else {
    filterThisWeek.value = false;
  }
};
const toggleLastWeekFilter = () => {
  if (!filterLastWeek.value) {
    setExclusiveTimeFilter('lastWeek');
  } else {
    filterLastWeek.value = false;
  }
};
const toggleNextMonthFilter = () => {
  if (!filterNextMonth.value) {
    setExclusiveTimeFilter('nextMonth');
  } else {
    filterNextMonth.value = false;
  }
};
const toggleLastMonthFilter = () => {
  if (!filterLastMonth.value) {
    setExclusiveTimeFilter('lastMonth');
  } else {
    filterLastMonth.value = false;
  }
};
const toggleNext3MonthsFilter = () => {
  if (!filterNext3Months.value) {
    setExclusiveTimeFilter('next3Months');
  } else {
    filterNext3Months.value = false;
  }
};
const toggleLast3MonthsFilter = () => {
  if (!filterLast3Months.value) {
    setExclusiveTimeFilter('last3Months');
  } else {
    filterLast3Months.value = false;
  }
};
const toggleNext6MonthsFilter = () => {
  if (!filterNext6Months.value) {
    setExclusiveTimeFilter('next6Months');
  } else {
    filterNext6Months.value = false;
  }
};
const toggleLast6MonthsFilter = () => {
  if (!filterLast6Months.value) {
    setExclusiveTimeFilter('last6Months');
  } else {
    filterLast6Months.value = false;
  }
};
const toggleThisYearFilter = () => {
  if (!filterThisYear.value) {
    setExclusiveTimeFilter('thisYear');
  } else {
    filterThisYear.value = false;
  }
};
const toggleNearMeFilter = () => {
  filterNearMe.value = !filterNearMe.value;
};
const toggleAvailableFilter = () => {
  filterAvailable.value = !filterAvailable.value;
};
const toggleTournamentFilter = () => {
  filterTournament.value = !filterTournament.value;
};

// Helper function for distance calculation (Haversine)
function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

const badgeFilteredMatches = computed(() => {
  let itemsToFilter = matchesFilteredByCalendar.value;

  // Week filters - handle both current week and last week
  if (filterThisWeek.value) {
    const today = dayjs().startOf('day');
    const firstDayOfWeek = today.startOf('week'); // Monday as start of week
    const lastDayOfWeek = today.endOf('week');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(firstDayOfWeek) && matchStartDate.isSameOrBefore(lastDayOfWeek);
    });
  }

  if (filterLastWeek.value) {
    const today = dayjs().startOf('day');
    const firstDayOfLastWeek = today.subtract(1, 'week').startOf('week');
    const lastDayOfLastWeek = today.subtract(1, 'week').endOf('week');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(firstDayOfLastWeek) && matchStartDate.isSameOrBefore(lastDayOfLastWeek);
    });
  }

  // Month filters - handle both next month and last month
  if (filterNextMonth.value) {
    const today = dayjs().startOf('day');
    const nextMonthStart = today.add(1, 'month').startOf('month');
    const nextMonthEnd = today.add(1, 'month').endOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(nextMonthStart) && matchStartDate.isSameOrBefore(nextMonthEnd);
    });
  }

  if (filterLastMonth.value) {
    const today = dayjs().startOf('day');
    const lastMonthStart = today.subtract(1, 'month').startOf('month');
    const lastMonthEnd = today.subtract(1, 'month').endOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(lastMonthStart) && matchStartDate.isSameOrBefore(lastMonthEnd);
    });
  }

  // 3-month filters - handle both next 3 months and last 3 months
  if (filterNext3Months.value) {
    const today = dayjs().startOf('day');
    const next3MonthsEnd = today.add(3, 'month').endOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(today) && matchStartDate.isSameOrBefore(next3MonthsEnd);
    });
  }

  if (filterLast3Months.value) {
    const today = dayjs().startOf('day');
    const last3MonthsStart = today.subtract(3, 'month').startOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(last3MonthsStart) && matchStartDate.isSameOrBefore(today);
    });
  }

  // 6-month filters - handle both next 6 months and last 6 months
  if (filterNext6Months.value) {
    const today = dayjs().startOf('day');
    const next6MonthsEnd = today.add(6, 'month').endOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(today) && matchStartDate.isSameOrBefore(next6MonthsEnd);
    });
  }

  if (filterLast6Months.value) {
    const today = dayjs().startOf('day');
    const last6MonthsStart = today.subtract(6, 'month').startOf('month');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(last6MonthsStart) && matchStartDate.isSameOrBefore(today);
    });
  }

  // "This year" filter
  if (filterThisYear.value) {
    const today = dayjs().startOf('day');
    const startOfYear = today.startOf('year');
    const endOfYear = today.endOf('year');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      return matchStartDate.isSameOrAfter(startOfYear) && matchStartDate.isSameOrBefore(endOfYear);
    });
  }

  // "Near me" filter
  if (filterNearMe.value) {
    // Use latitude/longitude from activePlayer in user store
    const playerLat = activePlayer.value?.latitude;
    const playerLng = activePlayer.value?.longitude;

    if (typeof playerLat === 'number' && typeof playerLng === 'number') {
      const nearRadiusKm = nearMeDistance.value[0]; // Use dynamic distance from slider

      itemsToFilter = itemsToFilter.filter(match => {
        if (typeof match.latitude !== 'number' || typeof match.longitude !== 'number') return false;
        const R = 6371; // Radius of the Earth in km
        const dLat = deg2rad(match.latitude - playerLat);
        const dLon = deg2rad(match.longitude - playerLng);
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(deg2rad(playerLat)) *
          Math.cos(deg2rad(match.latitude)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distanceKm = R * c;
        return distanceKm <= nearRadiusKm;
      });
    }
  }

  // "Available" filter
  if (filterAvailable.value) {
    const today = dayjs().startOf('day');

    itemsToFilter = itemsToFilter.filter(match => {
      if (!match.startDate) return false;
      const matchStartDate = dayjs(match.startDate).startOf('day');
      const isUpcoming = matchStartDate.isSameOrAfter(today);

      let registrationOpen = true;
      if (match.registrationEnds) {
        const matchRegEndDate = dayjs(match.registrationEnds).startOf('day');
        registrationOpen = matchRegEndDate.isSameOrAfter(today);
      }
      // Consider registrationFinished, assuming it's a boolean.
      // If it can be null/undefined, adjust accordingly.
      const notFinished = match.registrationFinished !== true;

      return isUpcoming && registrationOpen && notFinished;
    });
  }

  // "Tournament" filter
  if (filterTournament.value) {
    itemsToFilter = itemsToFilter.filter(match => {
      return !!match.tournamentConfirmedAt === true;
    });
  }

  // Federation filter
  if (!filtersStore.isAllFederationsSelected) {
    itemsToFilter = itemsToFilter.filter(match => {
      // If match has a federation, check if it's in the selected federations
      if (match.federation?.name) {
        return selectedFederations.value.includes(match.federation.name)
      }
      // If match has no federation, only show if 'ALL' is selected (which we already checked above)
      return false
    })
  }

  // Sorting logic
  const sortedItems = [...itemsToFilter].sort((a, b) => {
    if (sortBy.value === 'date') {
      return dayjs(a.startDate).diff(dayjs(b.startDate));
    } else if (sortBy.value === 'players') {
      return (b.matchRegistrationsCount || 0) - (a.matchRegistrationsCount || 0);
    } else if (sortBy.value === 'distance') {
      const playerLat = activePlayer.value?.latitude;
      const playerLng = activePlayer.value?.longitude;
      if (typeof playerLat !== 'number' || typeof playerLng !== 'number') return 0;

      const distA = (a.latitude !== undefined && a.longitude !== undefined) ? Math.sqrt(Math.pow(a.latitude - playerLat, 2) + Math.pow(a.longitude - playerLng, 2)) : Infinity;
      const distB = (b.latitude !== undefined && b.longitude !== undefined) ? Math.sqrt(Math.pow(b.latitude - playerLat, 2) + Math.pow(b.longitude - playerLng, 2)) : Infinity;
      return distA - distB;
    }
    return 0;
  });

  return sortedItems;
});



const handleSelectMatchFromMap = (matchId: string) => {
  // Corrected to use selectMatch and parse the ID to a number
  selectMatch(parseInt(matchId, 10));
};

// Responsive sidebar styles using VueUse
const { width } = useWindowSize();

const sidebarStyles = computed(() => {
  const styles = (() => {
    if (width.value >= 1280) {
      // xl and above: 25rem
      return {
        '--sidebar-width': '25rem',
        '--sidebar-width-mobile': '25rem'
      };
    } else if (width.value >= 768) {
      // md to xl: 15rem
      return {
        '--sidebar-width': '15rem',
        '--sidebar-width-mobile': '25rem'
      };
    }
    // Default/mobile: 25rem
    return {
      '--sidebar-width': '25rem',
      '--sidebar-width-mobile': '25rem'
    };
  })();

  // Debug logging
  console.log(`Window width: ${width.value}px, Sidebar width: ${styles['--sidebar-width']}`);

  return styles;
});

</script>

<template>
  <SidebarProvider :style="sidebarStyles">
    <SidebarInset>
      <div class="flex flex-col gap-4">
        <div v-if="isLoading" class="text-center p-4">
          Loading matches...
        </div>
        <div v-else-if="error" class="text-center p-4 text-red-500">
          Error loading matches: {{ error?.message }}
        </div>
        <div v-else>


              <div class="h-full">
                <div class="flex items-center justify-between gap-2 px-4 py-2">
                  <div class="flex items-center gap-2">
                    <!-- Mobile sidebar trigger -->
                    <SidebarTrigger class="md:hidden" />

                    <div class="flex items-center gap-2">
                      <h2 class="text-lg font-semibold">{{ t('navigation.matches') }}</h2>
                      <Badge variant="outline">{{ badgeFilteredMatches.length }}</Badge>
                    </div>
                  </div>

                  <!-- Create Match Button (only show if user has organizer role) -->
                  <Button
                    v-if="activeOrganizer"
                    @click="router.push({ name: 'match-create' })"
                    size="sm"
                    class="ml-auto"
                  >
                    <Plus class="w-4 h-4 mr-2" />
                    {{ t('matches.createMatch') }}
                  </Button>
                </div>


                <div class="bg-background/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                  <div class="flex items-center gap-2">
                    <div class="relative flex-1">
                      <Search class="absolute left-2 top-2.5 size-4 text-muted-foreground" />
                      <Input v-model="searchValue" placeholder="Search matches..." class="pl-8" />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      :class="{ 'bg-primary text-primary': showFilters }"
                      @click="toggleFiltersVisibility"
                    >
                      <Filter class="h-4 w-4" />
                      <span class="sr-only">{{ t('filters.toggleFilters') }}</span>
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="outline" class="flex items-center gap-1">
                          <span>Sort by: {{ sortBy }}</span>
                          <ChevronsUpDown class="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem @click="sortMatches('date')">Date</DropdownMenuItem>
                        <DropdownMenuItem @click="sortMatches('distance')">Distance</DropdownMenuItem>
                        <DropdownMenuItem @click="sortMatches('players')">Players</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div v-if="showFilters" class="mt-2 space-y-2">
                    <!-- Time-based filters -->
                    <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                      <!-- Current week filter (always available) -->
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterThisWeek }"
                        @click="toggleThisWeekFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.thisWeek') }}</span>
                      </Badge>

                      <!-- Past-specific filters for results/archive -->
                      <template v-if="props.matchType === 'results' || props.matchType === 'archive'">
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterLastWeek }"
                          @click="toggleLastWeekFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.lastWeek') }}</span>
                        </Badge>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterLastMonth }"
                          @click="toggleLastMonthFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.lastMonth') }}</span>
                        </Badge>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterLast3Months }"
                          @click="toggleLast3MonthsFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.last3Months') }}</span>
                        </Badge>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterLast6Months }"
                          @click="toggleLast6MonthsFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.last6Months') }}</span>
                        </Badge>
                      </template>

                      <!-- Future-specific filters for search/watched -->
                      <template v-else>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterNextMonth }"
                          @click="toggleNextMonthFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.nextMonth') }}</span>
                        </Badge>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterNext3Months }"
                          @click="toggleNext3MonthsFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.next3Months') }}</span>
                        </Badge>
                        <Badge
                          variant="outline"
                          class="flex items-center gap-1 cursor-pointer"
                          :class="{ 'border-primary text-primary': filterNext6Months }"
                          @click="toggleNext6MonthsFilter"
                        >
                          <CalendarIcon class="h-3 w-3" />
                          <span>{{ t('filters.next6Months') }}</span>
                        </Badge>
                      </template>

                      <!-- This year filter (always available) -->
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterThisYear }"
                        @click="toggleThisYearFilter"
                      >
                        <CalendarIcon class="h-3 w-3" />
                        <span>{{ t('filters.thisYear') }}</span>
                      </Badge>
                    </div>

                    <!-- Location and availability filters -->
                    <div class="flex items-center justify-start gap-2 overflow-x-auto pb-2">
                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterTournament }"
                        @click="toggleTournamentFilter"
                      >
                        <Trophy class="h-3 w-3" />
                        <span>{{ t('filters.tournaments') }}</span>
                      </Badge>

                      <Badge
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterNearMe }"
                        @click="toggleNearMeFilter"
                      >
                        <MapPin class="h-3 w-3" />
                        <span>{{ t('filters.nearMe') }}</span>
                      </Badge>

                      <!-- Inline distance slider for "Near me" filter -->
                      <div v-if="filterNearMe" class="flex items-center gap-2 px-2 py-1 bg-muted/30 rounded-md min-w-0">
                        <span class="text-xs text-muted-foreground whitespace-nowrap">{{ nearMeDistance[0] }}km</span>
                        <Slider
                          v-model="nearMeDistance"
                          :min="5"
                          :max="500"
                          :step="5"
                          class="w-20"
                        />
                      </div>

                      <!-- Available filter only for future matches -->
                      <Badge
                        v-if="props.matchType === 'search' || props.matchType === 'watched'"
                        variant="outline"
                        class="flex items-center gap-1 cursor-pointer"
                        :class="{ 'border-primary text-primary': filterAvailable }"
                        @click="toggleAvailableFilter"
                      >
                        <Users class="h-3 w-3" />
                        <span>{{ t('filters.available') }}</span>
                      </Badge>
                    </div>
                  </div>
                </div>

                <div class="h-[calc(100%-10rem)] overflow-auto">
                  <MatchesList
                    :items="badgeFilteredMatches"
                    :hovered-matches="hoveredMatches"
                    :current-match="currentMatch"
                    @select-match="selectMatch"
                    @hover-match="setHoveredMatchId"
                  />
                </div>
              </div>

        </div>
      </div>
    </SidebarInset>
    <Sidebar
      class="sticky top-14 h-[calc(100svh-3.5rem)] border-l"
      collapsible="offcanvas"
    >
      <SidebarContent class="flex flex-col gap-0">
        <div v-if="currentMatch" class="mb-0 border-b border-sidebar-border pb-0">
          <MatchDetailsWidget :match="currentMatch" />
        </div>

        <div class="mb-0 border-b border-sidebar-border pb-0">
          <div class="h-52">
            <ArcheryMap
              :latitude="mapLatitude"
              :longitude="mapLongitude"
              :matches-to-display="badgeFilteredMatches"
              :hovered-matches="hoveredMatches"
              :hovered-match-id="hoveredMatchId"
              @select-match="handleSelectMatchFromMap"
            />
          </div>
        </div>

        <div v-if="!currentMatch" class="flex-1">
          <EventCalendar
            :selected-date-range="selectedCalendarDateRange"
            :matches="calendarMatches"
            @update:selected-date-range="handleCalendarDateRangeUpdate"
            @hover:date="handleCalendarDateHover"
            @unhover:date="handleCalendarDateUnhover"
            @select:match="handleCalendarSelectMatch"
          />
        </div>
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
