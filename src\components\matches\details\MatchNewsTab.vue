<script setup lang="ts">
import { MessageCircle } from 'lucide-vue-next'
</script>

<template>
  <div class="space-y-4 mt-0">
    <div class="flex items-center gap-2 mb-4">
      <MessageCircle class="h-5 w-5" />
      <h3 class="text-lg font-semibold">News & Updates</h3>
    </div>
    <div class="text-center py-8 text-muted-foreground">
      <MessageCircle class="h-12 w-12 mx-auto mb-2 opacity-50" />
      <p>No news or updates available</p>
      <p class="text-sm">Check back later for match updates and announcements</p>
    </div>
  </div>
</template>
