<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useMatchesService } from '@/stores/matches'

import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText } from 'lucide-vue-next'
import type { Match, MatchRegistration } from '@/api/feathers-client'

import MatchHeader from '@/components/matches/details/MatchHeader.vue'
import MatchGeneralTab from '@/components/matches/details/MatchGeneralTab.vue'
import MatchPlayersTab from '@/components/matches/details/MatchPlayersTab.vue'
import MatchAgendaTab from '@/components/matches/details/MatchAgendaTab.vue'
import MatchNewsTab from '@/components/matches/details/MatchNewsTab.vue'
import MatchScoresTab from '@/components/matches/details/MatchScoresTab.vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const matchesService = useMatchesService()

const isLoading = ref(false)
const error = ref<Error | null>(null)

const matchId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

const match = ref<Match | null>(null)
const registrations = ref<MatchRegistration[]>([])
const activeTab = ref('general')

const playersCount = computed(() => registrations.value.length)

async function fetchMatchRegistrations() {
  if (!matchId.value) return

  try {
    const allRegistrations = await matchesService.findMatchRegistrations({
      query: {
        matchId: matchId.value,
        $populate: 'player'
      }
    })
    registrations.value = allRegistrations.filter(
      (reg: MatchRegistration) => reg.matchId === matchId.value
    )
  } catch (err) {
    console.error('Failed to fetch match registrations:', err)
    registrations.value = []
  }
}

onMounted(async () => {
  if (matchId.value) {
    isLoading.value = true
    error.value = null
    try {
      // Fetch match and registrations in parallel
      const [matchData] = await Promise.all([
        matchesService.getMatch(matchId.value),
        fetchMatchRegistrations()
      ])
      match.value = matchData
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('Failed to fetch match')
      console.error('Failed to fetch match:', err)
    } finally {
      isLoading.value = false
    }
  }
})

function isMatchInPast() {
  if (!match.value?.endDate) return false
  return new Date(match.value.endDate) < new Date()
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="min-h-screen bg-background">
    <div class="container px-4 py-6 ">
      <MatchHeader @back="goBack" />

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">Loading match details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-red-500 mb-4">Error loading match details</p>
          <p class="text-muted-foreground">{{ error.message }}</p>
          <Button variant="outline" @click="goBack" class="mt-4">
            Go Back
          </Button>
        </div>
      </div>

      <!-- Match Details -->
      <div v-else-if="match" class="flex flex-col lg:flex-row gap-6">
        <!-- Left Side: Tabbed Content -->
        <div class="flex-1">
          <Card>
            <Tabs v-model="activeTab" default-value="general" class="w-full">
              <CardHeader class="pb-3">
                <CardTitle class="flex items-center gap-2">
                  <FileText class="h-5 w-5" />
                  {{ match.name }}
                </CardTitle>
                <TabsList class="grid w-full grid-cols-5">
                  <TabsTrigger value="general">{{ t('tabs.general') }}</TabsTrigger>
                  <TabsTrigger value="players" class="flex items-center gap-2">
                    {{ t('tabs.players') }}
                    <Badge v-if="playersCount > 0" variant="outline">
                      {{ playersCount }}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="agenda">{{ t('tabs.agenda') }}</TabsTrigger>
                  <TabsTrigger value="news">{{ t('tabs.news') }}</TabsTrigger>
                  <TabsTrigger value="scores" :disabled="!isMatchInPast()">{{ t('tabs.scores') }}</TabsTrigger>
                </TabsList>
              </CardHeader>

              <CardContent class="space-y-4">
                <TabsContent value="general">
                  <MatchGeneralTab :match="match" />
                </TabsContent>
                <TabsContent value="players">
                  <MatchPlayersTab :match-id="match.id" :registrations="registrations" />
                </TabsContent>
                <TabsContent value="agenda">
                  <MatchAgendaTab :match="match" />
                </TabsContent>
                <TabsContent value="news">
                  <MatchNewsTab />
                </TabsContent>
                <TabsContent value="scores">
                  <MatchScoresTab :match="match" />
                </TabsContent>
              </CardContent>
            </Tabs>
          </Card>
        </div>

        <!-- Right Side: Match Details Widget -->
        <div class="w-full lg:w-96">
          <Card>
            <MatchDetailsWidget :match="match" />
          </Card>
        </div>
      </div>

      <!-- Not Found -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-muted-foreground mb-4">Match not found</p>
          <Button variant="outline" @click="goBack">
            Go Back
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
