<script setup lang="ts">
import type { Match } from '@/api/feathers-client'
import { Badge } from '@/components/ui/badge'
import { FileText, MapPin } from 'lucide-vue-next'

defineProps<{
  match: Match
}>()
</script>

<template>
  <div class="space-y-4 mt-0">
    <div class="flex items-center gap-2 mb-4">
      <FileText class="h-5 w-5" />
      <h3 class="text-lg font-semibold">Match Agenda</h3>
    </div>

    <div v-if="(match as any).agenda && (match as any).agenda.length > 0" class="space-y-3">
      <div
        v-for="(item, index) in (match as any).agenda"
        :key="index"
        class="border rounded-lg p-4 space-y-2"
      >
        <div class="flex items-center justify-between">
          <h4 class="font-medium">{{ item.name || `Agenda Item ${index + 1}` }}</h4>
          <div class="flex items-center gap-2 text-sm text-muted-foreground">
            <span v-if="item.date">{{ item.date }}</span>
            <span v-if="item.time">{{ item.time }}</span>
          </div>
        </div>
        <div v-if="item.description" class="text-sm text-muted-foreground">
          {{ item.description }}
        </div>
        <div v-if="item.location" class="flex items-center gap-1 text-sm text-muted-foreground">
          <MapPin class="w-3 h-3" />
          {{ item.location }}
        </div>
        <div class="flex flex-wrap gap-2 text-xs">
          <Badge v-if="item.competitionBranch" variant="outline">{{ item.competitionBranch }}</Badge>
          <Badge v-if="item.goals" variant="outline">{{ item.goals }} goals</Badge>
          <Badge v-if="item.numberOfArrows" variant="outline">{{ item.numberOfArrows }} arrows</Badge>
          <Badge v-if="item.typesOfGoals" variant="outline">{{ item.typesOfGoals }}</Badge>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8 text-muted-foreground">
      <FileText class="h-12 w-12 mx-auto mb-2 opacity-50" />
      <p>No agenda available</p>
      <p class="text-sm">The match agenda has not been published yet</p>
    </div>
  </div>
</template>
