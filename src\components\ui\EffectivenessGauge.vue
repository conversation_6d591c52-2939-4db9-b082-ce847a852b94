<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  ProgressIndicator,
  ProgressRoot,
} from 'reka-ui'
import { cn } from '@/lib/utils'

interface EffectivenessGaugeProps {
  /**
   * The effectiveness value (0-100)
   */
  value: number
  /**
   * Optional label to display above the gauge
   */
  label?: string
  /**
   * Show the numeric value next to the gauge
   */
  showValue?: boolean
  /**
   * Additional CSS classes
   */
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<EffectivenessGaugeProps>(), {
  showValue: true,
})

const { t } = useI18n()

// Ensure value is within 0-100 range
const normalizedValue = computed(() => Math.max(0, Math.min(100, props.value)))

// Determine color based on effectiveness value
const effectivenessColor = computed(() => {
  if (normalizedValue.value >= 80) return 'bg-green-500'
  if (normalizedValue.value >= 50) return 'bg-yellow-400'
  return 'bg-red-500'
})

// Get background color variant for the track
const trackColor = computed(() => {
  if (normalizedValue.value >= 80) return 'bg-green-500/20'
  if (normalizedValue.value >= 50) return 'bg-yellow-400/20'
  return 'bg-red-500/20'
})

// Get text color for the value display
const textColor = computed(() => {
  if (normalizedValue.value >= 80) return 'text-green-700'
  if (normalizedValue.value >= 50) return 'text-yellow-700'
  return 'text-red-700'
})

const displayLabel = computed(() => props.label !== undefined ? props.label : t('effectiveness.label'))
</script>

<template>
  <div :class="cn('space-y-2', props.class)">
    <!-- Label -->
    <div v-if="displayLabel && displayLabel.trim()" class="flex items-center justify-between">
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ displayLabel }}
      </span>
      <span
        v-if="showValue"
        :class="cn('text-sm font-semibold tabular-nums', textColor)"
      >
        {{ normalizedValue }}%
      </span>
    </div>

    <!-- Progress Bar -->
    <ProgressRoot
      :model-value="normalizedValue"
      :class="cn('relative h-2 w-full overflow-hidden rounded-full', trackColor)"
      :aria-label="`${displayLabel}: ${normalizedValue}%`"
      role="progressbar"
      :aria-valuenow="normalizedValue"
      aria-valuemin="0"
      aria-valuemax="100"
    >
      <ProgressIndicator
        :class="cn('h-full w-full flex-1 transition-all duration-300 ease-in-out', effectivenessColor)"
        :style="`transform: translateX(-${100 - normalizedValue}%);`"
      />
    </ProgressRoot>
  </div>
</template>
