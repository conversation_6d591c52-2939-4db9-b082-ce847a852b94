import { ref, watch } from 'vue'
import { useStorage } from '@vueuse/core'

export type Theme = 'light' | 'dark' | 'system'

/**
 * Theme composable for managing application theme
 * 
 * Features:
 * - Persists theme preference to localStorage
 * - Supports system theme detection
 * - Automatically applies theme classes to document
 */
export function useTheme() {
  // Persist theme preference
  const theme = useStorage<Theme>('ap-theme', 'system')
  
  // Current resolved theme (light/dark)
  const resolvedTheme = ref<'light' | 'dark'>('light')
  
  // System theme detection
  const systemTheme = ref<'light' | 'dark'>('light')
  
  // Update system theme based on media query
  function updateSystemTheme() {
    systemTheme.value = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  
  // Apply theme to document
  function applyTheme(targetTheme: 'light' | 'dark') {
    const root = document.documentElement
    
    if (targetTheme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    resolvedTheme.value = targetTheme
  }
  
  // Resolve theme based on preference
  function resolveTheme() {
    if (theme.value === 'system') {
      updateSystemTheme()
      applyTheme(systemTheme.value)
    } else {
      applyTheme(theme.value)
    }
  }
  
  // Set theme programmatically
  function setTheme(newTheme: Theme) {
    theme.value = newTheme
    resolveTheme()
  }
  
  // Force theme temporarily (for organizer mode)
  function forceTheme(targetTheme: 'light' | 'dark') {
    applyTheme(targetTheme)
  }
  
  // Initialize theme
  updateSystemTheme()
  resolveTheme()
  
  // Watch for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', () => {
    if (theme.value === 'system') {
      updateSystemTheme()
      resolveTheme()
    }
  })
  
  // Watch for theme preference changes
  watch(theme, resolveTheme)
  
  return {
    theme,
    resolvedTheme,
    systemTheme,
    setTheme,
    forceTheme,
    resolveTheme
  }
}
