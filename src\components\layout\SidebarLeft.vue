<script setup lang="ts">
import type { SidebarProps } from '@/components/ui/sidebar'
import { computed, onMounted } from 'vue'

import NavMain from '../NavMain.vue'
import NavUser from '../NavUser.vue'
import GearSwitcher from '../GearSwitcher.vue'
import SidebarFilters from '../SidebarFilters.vue'
import IconBowAndArrow from '../icons/IconBowAndArrow.vue'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar'

import {
  Target,
  Heart,
  Search,
  BarChart2,
  Mail,
  ClipboardCheck,
  Eye,
  Award,
  Archive,
  Trophy,
  Plus,
  Users,
  UserPlus
} from 'lucide-vue-next'
import { useI18n } from 'vue-i18n'
import { useFederationsStore } from '@/stores/federations'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const federationsStore = useFederationsStore()
const userStore = useUserStore()

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
  variant: 'sidebar',
})

// Split menu items into two groups
const matchesItems = [
  {
    title: t('navigation.matches'),
    url: '/',
    icon: Target,
    isActive: true,
  },
  {
    title: t('navigation.tournaments'),
    url: '/tournaments',
    icon: Trophy,
  },
  {
    title: t('navigation.mySignups'),
    url: '/matches/signups',
    icon: ClipboardCheck,
  },
  {
    title: t('navigation.watched'),
    url: '/matches/watched',
    icon: Eye,
  },
  {
    title: t('navigation.results'),
    url: '/matches/results',
    icon: Award,
  },
  {
    title: t('navigation.archive'),
    url: '/matches/archive',
    icon: Archive,
  },
]

const otherItems = [
  {
    title: t('navigation.statistics'),
    url: '/statistics',
    icon: BarChart2,
  },
  {
    title: t('navigation.favourites'),
    url: '/favourites',
    icon: Heart,
  },
  {
    title: t('navigation.messages'),
    url: '/messages',
    icon: Mail,
  },
]

// Organizer menu items - visible only when activeOrganizer is set
const organizerItems = [
  {
    title: t('navigation.newMatch'),
    url: '/matches/create',
    icon: Plus,
  },
  {
    title: t('navigation.newTournament'),
    url: '/tournaments/create',
    icon: Plus,
  },
  {
    title: t('navigation.joinTournament'),
    url: '/tournaments/join',
    icon: UserPlus,
  },
  {
    title: t('navigation.ourMatches'),
    url: '/organizer/matches',
    icon: Target,
  },
  {
    title: t('navigation.ourTournaments'),
    url: '/organizer/tournaments',
    icon: Trophy,
  },
  {
    title: t('navigation.ourPlayers'),
    url: '/organizer/players',
    icon: Users,
  },
  {
    title: t('navigation.ourResults'),
    url: '/organizer/results',
    icon: Award,
  },
]

// Load federations on component mount
onMounted(async () => {
  try {
    await federationsStore.getAllFederations()
  } catch (error) {
    console.error('Failed to load federations:', error)
  }
})

// Computed federation filter data
const federationFilters = computed(() => {
  const federationItems = ['ALL']

  // Add active federations from store
  if (federationsStore.activeFederations) {
    federationItems.push(...federationsStore.activeFederations.map(fed => fed.name))
  }

  return [
    {
      name: 'Federations',
      items: federationItems
    }
  ]
})

// This is sample data.
const data = {
  user: {
    name: 'Kowalewski Kazimierz',
    email: '<EMAIL>',
    avatar: '/avatar.jpg',
  },
  gears: [
    {
      name: 'Matthews RX36',
      logo: IconBowAndArrow,
      plan: 'AMFS',
    },
    {
      name: 'Hoyt Invicta',
      logo: IconBowAndArrow,
      plan: 'AMFU',
    },
    {
      name: 'PSE Perform-X',
      logo: IconBowAndArrow,
      plan: 'AMBH',
    },
  ],
  matchesItems,
  otherItems,
  organizerItems,
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <GearSwitcher :gears="data.gears" />
    </SidebarHeader>
    <SidebarContent>
      <NavMain
        :matchesItems="data.matchesItems"
        :otherItems="data.otherItems"
        :organizerItems="data.organizerItems"
        :showOrganizerMenu="!!userStore.activeOrganizer"
      />
      <SidebarSeparator class="mx-0" />
      <SidebarFilters :calendars="federationFilters" />
      <SidebarSeparator class="mx-0" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
