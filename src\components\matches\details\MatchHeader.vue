<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-vue-next'

const emit = defineEmits(['back'])

const goBack = () => {
  emit('back')
}
</script>

<template>
  <div class="flex items-center gap-4 mb-6">
    <Button variant="ghost" size="icon" @click="goBack">
      <ArrowLeft class="h-4 w-4" />
    </Button>
    <h1 class="text-2xl font-bold">Match Details</h1>
  </div>
</template>
