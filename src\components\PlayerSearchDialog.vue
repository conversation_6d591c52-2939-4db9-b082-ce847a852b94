<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { refDebounced } from '@vueuse/core'
import { usePlayersService } from '@/stores/players'
import { Search, User } from 'lucide-vue-next'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Skeleton } from '@/components/ui/skeleton'
import type { Player } from '@/api/feathers-client'

const props = defineProps<{
  open?: boolean
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  'select-player': [player: Player]
}>()

const { t } = useI18n()
const playersService = usePlayersService()

// State
const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const searchResults = ref<Player[]>([])
const isLoading = ref(false)
const error = ref<Error | null>(null)

// Computed
const isOpen = computed({
  get: () => props.open ?? false,
  set: (value) => emit('update:open', value)
})

// Watch for search query changes
watch(debouncedSearchQuery, async (query) => {
  if (!query.trim()) {
    searchResults.value = []
    return
  }

  if (query.trim().length < 2) {
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const results = await playersService.searchPlayers(query.trim(), 20)
    searchResults.value = results
  } catch (err) {
    console.error('Failed to search players:', err)
    error.value = err instanceof Error ? err : new Error('Failed to search players')
    searchResults.value = []
  } finally {
    isLoading.value = false
  }
})

// Methods
function selectPlayer(player: Player) {
  emit('select-player', player)
  isOpen.value = false
  searchQuery.value = ''
  searchResults.value = []
}

function onOpenChange(open: boolean) {
  isOpen.value = open
  if (!open) {
    searchQuery.value = ''
    searchResults.value = []
    error.value = null
  }
}
</script>

<template>
  <Sheet :open="isOpen" @update:open="onOpenChange">
    <SheetContent class="sm:max-w-md">
      <SheetHeader>
        <SheetTitle>{{ t('statistics.searchPlayer') }}</SheetTitle>
        <SheetDescription>
          {{ t('statistics.searchPlayerDescription') }}
        </SheetDescription>
      </SheetHeader>

      <div class="space-y-4">
        <!-- Search Input -->
        <div class="relative">
          <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            :placeholder="t('statistics.searchPlayerPlaceholder')"
            class="pl-8"
            autofocus
          />
        </div>

        <!-- Search Results -->
        <div class="max-h-60 overflow-y-auto space-y-2">
          <!-- Loading State -->
          <div v-if="isLoading" class="space-y-2">
            <div v-for="i in 3" :key="i" class="flex items-center gap-3 p-2">
              <Skeleton class="h-8 w-8 rounded-full" />
              <div class="space-y-1 flex-1">
                <Skeleton class="h-4 w-32" />
                <Skeleton class="h-3 w-24" />
              </div>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="text-center py-4 text-destructive">
            <p class="text-sm">{{ error.message }}</p>
          </div>

          <!-- No Results -->
          <div v-else-if="debouncedSearchQuery && searchResults.length === 0 && !isLoading" class="text-center py-4 text-muted-foreground">
            <User class="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">{{ t('statistics.noPlayersFound') }}</p>
          </div>

          <!-- Results List -->
          <div v-else-if="searchResults.length > 0" class="space-y-1">
            <Button
              v-for="player in searchResults"
              :key="player.id"
              variant="ghost"
              class="w-full justify-start h-auto p-2"
              @click="selectPlayer(player)"
            >
              <Avatar class="h-8 w-8 mr-3">
                <AvatarImage v-if="player.avatar" :src="player.avatar" />
                <AvatarFallback class="text-xs">
                  {{ player.firstname?.[0] }}{{ player.lastname?.[0] }}
                </AvatarFallback>
              </Avatar>
              <div class="text-left">
                <div class="font-medium">
                  {{ player.firstname }} {{ player.lastname }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ player.city }}, {{ player.country }}
                </div>
              </div>
            </Button>
          </div>

          <!-- Initial State -->
          <div v-else-if="!debouncedSearchQuery" class="text-center py-4 text-muted-foreground">
            <Search class="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">{{ t('statistics.startTypingToSearch') }}</p>
          </div>
        </div>
      </div>
    </SheetContent>
  </Sheet>
</template>
