<script setup lang="ts">
import type { Match } from '@/api/feathers-client'

defineProps<{
  match: Match
}>()
</script>

<template>
  <div class="space-y-4 mt-0">
    <div>
      <h3 class="text-lg font-semibold mb-2">Description</h3>
      <div class="text-muted-foreground">
        <p style="white-space: pre-wrap;" v-if="match.description">{{ match.description }}</p>
        <p v-else class="italic">No description provided for this match.</p>
      </div>
    </div>

    <div v-if="(match as any).rules" class="space-y-2">
      <h3 class="text-lg font-semibold">Rules</h3>
      <div class="text-muted-foreground">
        <p>{{ (match as any).rules }}</p>
      </div>
    </div>

    <div v-if="(match as any).location" class="space-y-2">
      <h3 class="text-lg font-semibold">Location Details</h3>
      <div class="text-muted-foreground">
        <p>{{ (match as any).location }}</p>
        <p v-if="match.address">{{ match.address }}</p>
      </div>
    </div>
  </div>
</template>
