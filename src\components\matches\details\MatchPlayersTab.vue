<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMatchesService } from '@/stores/matches'
import type { MatchRegistration } from '@/api/feathers-client'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Users } from 'lucide-vue-next'

const props = defineProps<{
  matchId: number
  registrations?: MatchRegistration[]
}>()

const { t } = useI18n()
const matchesService = useMatchesService()
const localRegistrations = ref<MatchRegistration[]>([])
const isLoadingRegistrations = ref(false)
const registrationsError = ref<Error | null>(null)

// Filter states
const selectedStyleDivisions = ref<Set<string>>(new Set())
const selectedAgeDivisions = ref<Set<string>>(new Set())
const selectedGenderDivisions = ref<Set<string>>(new Set())

const registrations = computed(() => {
  return props.registrations || localRegistrations.value
})

// Available filter options
const availableStyleDivisions = computed(() => {
  const divisions = new Set<string>()
  registrations.value.forEach(registration => {
    if (registration.styleDivision) divisions.add(registration.styleDivision)
  })
  return Array.from(divisions).sort()
})

const availableAgeDivisions = computed(() => {
  const divisions = new Set<string>()
  registrations.value.forEach(registration => {
    if (registration.ageDivision) divisions.add(registration.ageDivision)
  })
  return Array.from(divisions).sort()
})

const availableGenderDivisions = computed(() => {
  const divisions = new Set<string>()
  registrations.value.forEach(registration => {
    if (registration.genderDivision) divisions.add(registration.genderDivision)
  })
  return Array.from(divisions).sort()
})

// Filtered registrations
const filteredRegistrations = computed(() => {
  let filtered = registrations.value

  if (selectedStyleDivisions.value.size > 0) {
    filtered = filtered.filter(registration =>
      registration.styleDivision && selectedStyleDivisions.value.has(registration.styleDivision)
    )
  }

  if (selectedAgeDivisions.value.size > 0) {
    filtered = filtered.filter(registration =>
      registration.ageDivision && selectedAgeDivisions.value.has(registration.ageDivision)
    )
  }

  if (selectedGenderDivisions.value.size > 0) {
    filtered = filtered.filter(registration =>
      registration.genderDivision && selectedGenderDivisions.value.has(registration.genderDivision)
    )
  }

  return filtered
})

// Filter toggle functions
function toggleStyleDivision(division: string) {
  if (selectedStyleDivisions.value.has(division)) {
    selectedStyleDivisions.value.delete(division)
  } else {
    selectedStyleDivisions.value.add(division)
  }
}

function toggleAgeDivision(division: string) {
  if (selectedAgeDivisions.value.has(division)) {
    selectedAgeDivisions.value.delete(division)
  } else {
    selectedAgeDivisions.value.add(division)
  }
}

function toggleGenderDivision(division: string) {
  if (selectedGenderDivisions.value.has(division)) {
    selectedGenderDivisions.value.delete(division)
  } else {
    selectedGenderDivisions.value.add(division)
  }
}

async function fetchMatchRegistrations() {
  if (props.registrations) return // Skip fetching if registrations are provided as prop

  isLoadingRegistrations.value = true
  registrationsError.value = null

  try {
    const allRegistrations = await matchesService.findMatchRegistrations({
      query: {
        matchId: props.matchId,
        $populate: 'player'
      }
    })
    localRegistrations.value = allRegistrations.filter(
      (reg: MatchRegistration) => reg.matchId === props.matchId
    )
  } catch (err) {
    console.error('Failed to fetch match registrations:', err)
    registrationsError.value = err instanceof Error ? err : new Error('Failed to fetch registrations')
    localRegistrations.value = []
  } finally {
    isLoadingRegistrations.value = false
  }
}

onMounted(fetchMatchRegistrations)
</script>

<template>
  <div class="space-y-4 mt-0">
    <div class="flex items-center gap-2 mb-4">
      <Users class="h-5 w-5" />
      <h3 class="text-lg font-semibold">{{ t('tournament.players') }}</h3>
      <span v-if="registrations.length > 0" class="text-sm text-muted-foreground">({{ registrations.length }})</span>
    </div>

    <div v-if="isLoadingRegistrations" class="flex items-center justify-center py-8">
      <div class="text-center">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
        <p class="text-muted-foreground text-sm">{{ t('common.loading') }}</p>
      </div>
    </div>

    <div v-else-if="registrationsError" class="text-center py-8 text-red-500">
      <p>Error loading players</p>
      <p class="text-sm text-muted-foreground">{{ registrationsError.message }}</p>
    </div>

    <div v-else-if="registrations.length > 0" class="space-y-4">
      <!-- Filters -->
      <div class="space-y-3">
        <div v-if="availableStyleDivisions.length > 0">
          <h4 class="text-sm font-medium mb-2">{{ t('matches.styleDivisions') }}</h4>
          <div class="flex flex-wrap gap-2">
            <Button
              v-for="styleDivision in availableStyleDivisions"
              :key="styleDivision"
              :variant="selectedStyleDivisions.has(styleDivision) ? 'default' : 'outline'"
              size="sm"
              @click="toggleStyleDivision(styleDivision)"
            >
              {{ styleDivision }}
            </Button>
          </div>
        </div>

        <div v-if="availableAgeDivisions.length > 0">
          <h4 class="text-sm font-medium mb-2">{{ t('matches.ageDivisions') }}</h4>
          <div class="flex flex-wrap gap-2">
            <Button
              v-for="ageDivision in availableAgeDivisions"
              :key="ageDivision"
              :variant="selectedAgeDivisions.has(ageDivision) ? 'default' : 'outline'"
              size="sm"
              @click="toggleAgeDivision(ageDivision)"
            >
              {{ ageDivision }}
            </Button>
          </div>
        </div>

        <div v-if="availableGenderDivisions.length > 0">
          <h4 class="text-sm font-medium mb-2">{{ t('matches.results.genderDivisions') }}</h4>
          <div class="flex flex-wrap gap-2">
            <Button
              v-for="genderDivision in availableGenderDivisions"
              :key="genderDivision"
              :variant="selectedGenderDivisions.has(genderDivision) ? 'default' : 'outline'"
              size="sm"
              @click="toggleGenderDivision(genderDivision)"
            >
              {{ genderDivision }}
            </Button>
          </div>
        </div>
      </div>

      <!-- Player List -->
      <div class="space-y-2">
        <div
          v-if="filteredRegistrations.length > 0"
          class="text-sm text-muted-foreground mb-2"
        >
          Showing {{ filteredRegistrations.length }} of {{ registrations.length }} players
        </div>

        <div v-if="filteredRegistrations.length > 0" class="space-y-2">
          <div v-for="registration in filteredRegistrations" :key="registration.id" class="flex items-center justify-between p-3 border rounded-lg">
            <div>
              <p class="font-medium">
                {{ (registration.player as any)?.name || `${(registration.player as any)?.firstname || ''} ${(registration.player as any)?.lastname || ''}`.trim() || 'Unknown Player' }}
              </p>
              <p class="text-sm text-muted-foreground">
                {{ (registration.player as any)?.club || 'No club' }}
              </p>
            </div>
            <div class="text-right flex gap-1 items-end">
              <Badge v-if="registration.styleDivision" variant="default">{{ registration.styleDivision }}</Badge>
              <Badge v-else variant="secondary">N/A</Badge>
              <Badge v-if="registration.ageDivision" variant="secondary">{{ registration.ageDivision }}</Badge>
              <Badge v-if="registration.genderDivision" variant="outline">{{ registration.genderDivision }}</Badge>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-muted-foreground">
          <Users class="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No players match the selected filters</p>
          <p class="text-sm">Try adjusting your filter selection</p>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8 text-muted-foreground">
      <Users class="h-12 w-12 mx-auto mb-2 opacity-50" />
      <p>No players registered yet</p>
    </div>
  </div>
</template>
