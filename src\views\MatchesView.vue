<script setup lang="ts">
import MatchesContainer from '@/components/matches/MatchesContainer.vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()

// Determine match type based on route name
const matchType = computed(() => {
  switch (route.name) {
    case 'matches-results':
      return 'results'
    case 'matches-archive':
      return 'archive'
    case 'matches-watched':
      return 'watched'
    default:
      return 'search'
  }
})
</script>

<template>
  <MatchesContainer :match-type="matchType" />
</template>
