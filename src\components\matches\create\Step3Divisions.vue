<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, ChevronDown, ChevronUp } from 'lucide-vue-next'
import type { AgeDivision, StyleDivision, Federation } from '@/stores/federations'

const { t } = useI18n()

const props = defineProps<{
  ageDivisions: AgeDivision[]
  styleDivisions: StyleDivision[]
  isFederationBased: boolean
  federationId?: number
  federations: Federation[]
  selectedFederation?: { name: string; ageDivisions: AgeDivision[]; styleDivisions: StyleDivision[] }
}>()

const emit = defineEmits<{
  (e: 'update:ageDivisions', value: AgeDivision[]): void
  (e: 'update:styleDivisions', value: StyleDivision[]): void
  (e: 'update:federationId', value: number | undefined): void
}>()

const expandedAgeDivisions = ref<Set<number>>(new Set())
const expandedStyleDivisions = ref<Set<number>>(new Set())

function toggleAgeDivision(index: number) {
  if (expandedAgeDivisions.value.has(index)) {
    expandedAgeDivisions.value.delete(index)
  } else {
    expandedAgeDivisions.value.clear()
    expandedAgeDivisions.value.add(index)
  }
}

function toggleStyleDivision(index: number) {
  if (expandedStyleDivisions.value.has(index)) {
    expandedStyleDivisions.value.delete(index)
  } else {
    expandedStyleDivisions.value.clear()
    expandedStyleDivisions.value.add(index)
  }
}

function isAgeDivisionExpanded(index: number) {
  return expandedAgeDivisions.value.has(index)
}

function isStyleDivisionExpanded(index: number) {
  return expandedStyleDivisions.value.has(index)
}

function addAgeDivision() {
  const currentDivisions = props.ageDivisions
  const newIndex = currentDivisions.length
  const newDivisions = [
    ...currentDivisions,
    {
      id: Date.now().toString(),
      name: '',
      short_name: '',
      min: undefined,
      max: undefined,
      isOpen: false
    }
  ]
  emit('update:ageDivisions', newDivisions)
  expandedAgeDivisions.value.clear()
  expandedAgeDivisions.value.add(newIndex)
}

function removeAgeDivision(index: number) {
  const newDivisions = [...props.ageDivisions]
  newDivisions.splice(index, 1)
  emit('update:ageDivisions', newDivisions)

  const newExpanded = new Set<number>()
  expandedAgeDivisions.value.forEach(expandedIndex => {
    if (expandedIndex < index) {
      newExpanded.add(expandedIndex)
    } else if (expandedIndex > index) {
      newExpanded.add(expandedIndex - 1)
    }
  })
  expandedAgeDivisions.value = newExpanded
}

function addStyleDivision() {
  const currentDivisions = props.styleDivisions
  const newIndex = currentDivisions.length
  const newDivisions = [
    ...currentDivisions,
    {
      id: Date.now().toString(),
      name: '',
      short_name: ''
    }
  ]
  emit('update:styleDivisions', newDivisions)
  expandedStyleDivisions.value.clear()
  expandedStyleDivisions.value.add(newIndex)
}

function removeStyleDivision(index: number) {
  const newDivisions = [...props.styleDivisions]
  newDivisions.splice(index, 1)
  emit('update:styleDivisions', newDivisions)

  const newExpanded = new Set<number>()
  expandedStyleDivisions.value.forEach(expandedIndex => {
    if (expandedIndex < index) {
      newExpanded.add(expandedIndex)
    } else if (expandedIndex > index) {
      newExpanded.add(expandedIndex - 1)
    }
  })
  expandedStyleDivisions.value = newExpanded
}

// Federation-based selection logic
function isAgeDivisionSelected(selected: AgeDivision[], division: AgeDivision): boolean {
  return selected.some(d => d.short_name === division.short_name)
}
function isStyleDivisionSelected(selected: StyleDivision[], division: StyleDivision): boolean {
  return selected.some(d => d.short_name === division.short_name)
}
function toggleFederationAgeDivision(division: AgeDivision, checked: boolean) {
  let newDivisions: AgeDivision[]
  if (checked) {
    newDivisions = [...props.ageDivisions, division]
  } else {
    newDivisions = props.ageDivisions.filter(d => d.short_name !== division.short_name)
  }
  emit('update:ageDivisions', newDivisions)
}
function toggleFederationStyleDivision(division: StyleDivision, checked: boolean) {
  let newDivisions: StyleDivision[]
  if (checked) {
    newDivisions = [...props.styleDivisions, division]
  } else {
    newDivisions = props.styleDivisions.filter(d => d.short_name !== division.short_name)
  }
  emit('update:styleDivisions', newDivisions)
}
</script>

<template>
  <div class="space-y-4">
    <!-- Federation Selection -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">{{ t('matches.divisionsInfo') }}</h3>
      <div class="flex items-center gap-2">
        <Label for="federation" class="text-sm font-medium">{{ t('matches.federation') }}</Label>
        <Select
          :model-value="federationId?.toString() || 'none'"
          @update:model-value="emit('update:federationId', $event === 'none' ? undefined : parseInt($event as string))"
        >
          <SelectTrigger class="w-48">
            <SelectValue :placeholder="t('matches.selectFederation')" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">
              {{ t('matches.noneFederation') }}
            </SelectItem>
            <SelectItem
              v-for="federation in federations"
              :key="federation.id"
              :value="federation.id.toString()"
            >
              {{ federation.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Age Divisions -->
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <Label class="text-sm font-semibold">{{ t('matches.ageDivisions') }}</Label>
          <Button
            v-if="!isFederationBased"
            @click="addAgeDivision"
            variant="outline"
            size="sm"
          >
            <Plus class="w-4 h-4 mr-2" />
            {{ t('matches.addAgeDivision') }}
          </Button>
        </div>

        <div v-if="ageDivisions.length === 0 && !isFederationBased" class="text-center py-3 border-2 border-dashed border-muted rounded-lg">
          <p class="text-muted-foreground text-sm">{{ t('matches.noAgeDivisions') }}</p>
          <Button @click="addAgeDivision" variant="outline" size="sm" class="mt-2">
            <Plus class="w-4 h-4 mr-2" />
            {{ t('matches.addAgeDivision') }}
          </Button>
        </div>

        <div v-else class="space-y-2">
          <!-- Federation-based age divisions -->
          <template v-if="isFederationBased">
            <div v-if="!selectedFederation || selectedFederation.ageDivisions.length === 0" class="text-center py-3 border-2 border-dashed border-muted rounded-lg">
              <p class="text-muted-foreground text-sm">{{ t('matches.noFederationAgeDivisions') }}</p>
            </div>
            <div v-else class="p-3 border rounded-lg">
              <p class="text-xs text-muted-foreground mb-2">{{ t('matches.selectMultipleAgeDivisions') }}</p>
              <div class="grid grid-cols-1 gap-2">
                <div
                  v-for="fedDivision in selectedFederation.ageDivisions"
                  :key="fedDivision.id"
                  class="flex items-center space-x-2"
                >
<Checkbox
  :id="`age-div-${fedDivision.short_name}`"
  :modelValue="!!isAgeDivisionSelected(ageDivisions, fedDivision)"
  @update:modelValue="(val: boolean | 'indeterminate') => toggleFederationAgeDivision(fedDivision, !!val)"
/>
                  <Label
                    :for="`age-div-${fedDivision.short_name}`"
                    class="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    {{ fedDivision.name }} ({{ fedDivision.short_name }})
                  </Label>
                </div>
              </div>
            </div>
          </template>

          <!-- Custom age divisions -->
          <template v-else>
            <div
              v-for="(division, index) in ageDivisions"
              :key="index"
              class="border rounded-lg overflow-hidden"
            >
              <!-- Collapsed Header -->
              <div
                class="p-2 bg-muted/30 cursor-pointer flex items-center justify-between hover:bg-muted/50 transition-colors"
                @click="toggleAgeDivision(index)"
              >
                <div class="flex items-center gap-2 flex-1 min-w-0">
                  <div class="flex items-center gap-1">
                    <component
                      :is="isAgeDivisionExpanded(index) ? ChevronUp : ChevronDown"
                      class="w-3 h-3 text-muted-foreground"
                    />
                    <span class="font-medium text-sm">{{ division.name || `${t('matches.ageDivision')} ${index + 1}` }}</span>
                  </div>
                  <div class="flex items-center gap-2 text-xs text-muted-foreground truncate">
                    <span v-if="division.short_name">{{ division.short_name }}</span>
                    <span v-if="division.min || division.max">
                      {{ division.min || '0' }}-{{ division.max || '∞' }}
                    </span>
                  </div>
                </div>
                <Button
                  @click.stop="removeAgeDivision(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive h-6 w-6 p-0"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <!-- Expanded Content -->
              <div v-if="isAgeDivisionExpanded(index)" class="p-4 space-y-3 border-t bg-background">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div class="space-y-1">
                    <Label :for="`age-short-name-${index}`">{{ t('matches.shortName') }} *</Label>
                    <Input
                      :id="`age-short-name-${index}`"
                      v-model="division.short_name"
                      :placeholder="t('matches.shortName')"
                    />
                  </div>
                  <div class="space-y-1">
                    <Label :for="`age-name-${index}`">{{ t('matches.name') }}</Label>
                    <Input
                      :id="`age-name-${index}`"
                      v-model="division.name"
                      :placeholder="t('matches.name')"
                    />
                  </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  <div class="space-y-1">
                    <Label :for="`age-min-${index}`">{{ t('matches.minAge') }}</Label>
                    <Input
                      :id="`age-min-${index}`"
                      v-model="division.min"
                      type="number"
                      min="0"
                      max="100"
                      :placeholder="t('matches.minAge')"
                    />
                  </div>
                  <div class="space-y-1">
                    <Label :for="`age-max-${index}`">{{ t('matches.maxAge') }}</Label>
                    <Input
                      :id="`age-max-${index}`"
                      v-model="division.max"
                      type="number"
                      min="0"
                      max="100"
                      :placeholder="t('matches.maxAge')"
                    />
                  </div>
                  <div class="flex items-center space-x-2 pt-6">
<Checkbox
  :id="`age-open-${index}`"
  :modelValue="!!division.isOpen"
  @update:modelValue="(val: boolean | 'indeterminate') => division.isOpen = !!val"
/>
                    <Label :for="`age-open-${index}`">{{ t('matches.isOpen') }}</Label>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- Style Divisions -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <Label class="text-base font-semibold">{{ t('matches.styleDivisions') }}</Label>
          <Button
            v-if="!isFederationBased"
            @click="addStyleDivision"
            variant="outline"
            size="sm"
          >
            <Plus class="w-4 h-4 mr-2" />
            {{ t('matches.addStyleDivision') }}
          </Button>
        </div>

        <div v-if="styleDivisions.length === 0 && !isFederationBased" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
          <p class="text-muted-foreground">{{ t('matches.noStyleDivisions') }}</p>
          <Button @click="addStyleDivision" variant="outline" class="mt-2">
            <Plus class="w-4 h-4 mr-2" />
            {{ t('matches.addStyleDivision') }}
          </Button>
        </div>

        <div v-else class="space-y-3">
          <!-- Federation-based style divisions -->
          <template v-if="isFederationBased">
            <div v-if="!selectedFederation || selectedFederation.styleDivisions.length === 0" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
              <p class="text-muted-foreground">{{ t('matches.noFederationStyleDivisions') }}</p>
            </div>
            <div v-else class="p-4 border rounded-lg">
              <p class="text-sm text-muted-foreground mb-4">{{ t('matches.selectMultipleStyleDivisions') }}</p>
              <div class="grid grid-cols-1 gap-3">
                <div
                  v-for="fedDivision in selectedFederation.styleDivisions"
                  :key="fedDivision.id"
                  class="flex items-center space-x-2"
                >
<Checkbox
  :id="`style-div-${fedDivision.short_name}`"
  :modelValue="!!isStyleDivisionSelected(styleDivisions, fedDivision)"
  @update:modelValue="(val: boolean | 'indeterminate') => toggleFederationStyleDivision(fedDivision, !!val)"
/>
                  <Label
                    :for="`style-div-${fedDivision.short_name}`"
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    {{ fedDivision.name }} ({{ fedDivision.short_name }})
                  </Label>
                </div>
              </div>
            </div>
          </template>

          <!-- Custom style divisions -->
          <template v-else>
            <div
              v-for="(division, index) in styleDivisions"
              :key="`custom-style-${index}`"
              class="border rounded-lg overflow-hidden"
            >
              <!-- Collapsed Header -->
              <div
                class="p-4 bg-muted/30 cursor-pointer flex items-center justify-between hover:bg-muted/50 transition-colors"
                @click="toggleStyleDivision(index)"
              >
                <div class="flex items-center gap-3 flex-1 min-w-0">
                  <div class="flex items-center gap-2">
                    <component
                      :is="isStyleDivisionExpanded(index) ? ChevronUp : ChevronDown"
                      class="w-4 h-4 text-muted-foreground"
                    />
                    <span class="font-medium">{{ division.name || `${t('matches.styleDivision')} ${index + 1}` }}</span>
                  </div>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground truncate">
                    <span v-if="division.short_name">{{ division.short_name }}</span>
                  </div>
                </div>
                <Button
                  @click.stop="removeStyleDivision(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <!-- Expanded Content -->
              <div v-if="isStyleDivisionExpanded(index)" class="p-4 space-y-3 border-t bg-background">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div class="space-y-1">
                    <Label :for="`style-short-name-${index}`">{{ t('matches.shortName') }} *</Label>
                    <Input
                      :id="`style-short-name-${index}`"
                      v-model="division.short_name"
                      :placeholder="t('matches.shortName')"
                    />
                  </div>
                  <div class="space-y-1">
                    <Label :for="`style-name-${index}`">{{ t('matches.name') }} *</Label>
                    <Input
                      :id="`style-name-${index}`"
                      v-model="division.name"
                      :placeholder="t('matches.name')"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- Federation Info -->
    <div v-if="isFederationBased && selectedFederation" class="p-4 bg-muted/30 rounded-lg">
      <p class="text-sm text-muted-foreground">
        {{ t('matches.federationDivisionsInfo', { federation: selectedFederation.name }) }}
      </p>
    </div>
  </div>
</template>
