<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useMatchesService } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import type { Match, MatchResult } from '@/api/feathers-client'
import PlayerResultWidget from '@/components/matches/PlayerResultWidget.vue'
import EffectivenessGauge from '@/components/ui/EffectivenessGauge.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Medal, Trophy } from 'lucide-vue-next'

const props = defineProps<{
  match: Match
}>()

const { t } = useI18n()
const matchesService = useMatchesService()
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

const results = ref<MatchResult[]>([])
const isLoadingResults = ref(false)
const resultsError = ref<Error | null>(null)
const selectedDivision = ref<string>('all')

// Filter states for "All Divisions" tab
const selectedStyleDivisions = ref<Set<string>>(new Set())
const selectedAgeDivisions = ref<Set<string>>(new Set())
const selectedGenderDivisions = ref<Set<string>>(new Set())

// Ref for scrolling to current player's result
const currentPlayerResultRef = ref<HTMLElement | null>(null)

function isMatchInPast() {
  if (!props.match?.endDate) return false
  return new Date(props.match.endDate) < new Date()
}

async function fetchMatchResults() {
  if (!props.match.id) return

  isLoadingResults.value = true
  resultsError.value = null

  try {
    const allResults = await matchesService.findMatchResults({
      query: {
        matchId: props.match.id,
        $populate: 'player',
        $limit: 500
      }
    })
    results.value = allResults || []
  } catch (err) {
    console.error('Failed to fetch match results:', err)
    resultsError.value = err instanceof Error ? err : new Error('Failed to fetch results')
    results.value = []
  } finally {
    isLoadingResults.value = false
  }
}

onMounted(() => {
  if (isMatchInPast()) {
    fetchMatchResults()
  }
})

// Check if a style division is "open" (no gender distinction)
function isStyleDivisionOpen(styleDivision: string): boolean {
  // First check if the match has styleDivisions config
  const matchStyleDivisions = (props.match as any)?.styleDivisions
  if (Array.isArray(matchStyleDivisions)) {
    const styleConfig = matchStyleDivisions.find((div: any) => {
      if (typeof div === 'object') {
        return div.short_name === styleDivision || div.name === styleDivision
      }
      return div === styleDivision
    })
    if (styleConfig && typeof styleConfig === 'object') {
      return styleConfig.open === 1 || styleConfig.open === '1' || styleConfig.open === true
    }
  }

  // Check federation styleDivisions
  const federationStyleDivisions = (props.match as any)?.federation?.styleDivisions
  if (Array.isArray(federationStyleDivisions)) {
    const styleConfig = federationStyleDivisions.find((div: any) => {
      if (typeof div === 'object') {
        return div.short_name === styleDivision || div.name === styleDivision
      }
      return div === styleDivision
    })
    if (styleConfig && typeof styleConfig === 'object') {
      return styleConfig.open === 1 || styleConfig.open === '1' || styleConfig.open === true
    }
  }

  return false
}

// Create division key for grouping results
function createDivisionKey(result: MatchResult): string {
  const style = result.styleDivision || ''
  const age = result.ageDivision || ''
  const gender = result.genderDivision || ''

  // If style division is open, ignore gender
  const isOpen = style && isStyleDivisionOpen(style)

  if (isOpen) {
    return `${style} ${age}`.trim()
  } else {
    return `${style} ${age} ${gender}`.trim()
  }
}

// Group results by division
const groupedResults = computed(() => {
  const groups: Record<string, MatchResult[]> = {}

  results.value.forEach(result => {
    const divisionKey = createDivisionKey(result)
    if (!groups[divisionKey]) {
      groups[divisionKey] = []
    }
    groups[divisionKey].push(result)
  })

  // Sort each group by place (ascending), then by points (descending) as fallback
  Object.keys(groups).forEach(key => {
    groups[key].sort((a, b) => {
      const placeA = a.place || 999
      const placeB = b.place || 999
      if (placeA !== placeB) {
        return placeA - placeB
      }
      return (b.points || 0) - (a.points || 0)
    })
  })

  return groups
})

// Available division tabs
const availableDivisions = computed(() => {
  const divisions = Object.keys(groupedResults.value).sort()
  return divisions.length > 1 ? ['all', ...divisions] : divisions
})

// Available filter options for "All Divisions" tab
const availableStyleDivisions = computed(() => {
  const divisions = new Set<string>()
  results.value.forEach(result => {
    if (result.styleDivision) divisions.add(result.styleDivision)
  })
  return Array.from(divisions).sort()
})

const availableAgeDivisions = computed(() => {
  const divisions = new Set<string>()
  results.value.forEach(result => {
    if (result.ageDivision) divisions.add(result.ageDivision)
  })
  return Array.from(divisions).sort()
})

const availableGenderDivisions = computed(() => {
  const divisions = new Set<string>()
  results.value.forEach(result => {
    if (result.genderDivision) divisions.add(result.genderDivision)
  })
  return Array.from(divisions).sort()
})

// Grouped and filtered results for display
const groupedFilteredResults = computed(() => {
  if (selectedDivision.value === 'all') {
    // Apply filters when in "All Divisions" tab
    let filtered = results.value

    if (selectedStyleDivisions.value.size > 0) {
      filtered = filtered.filter(result =>
        result.styleDivision && selectedStyleDivisions.value.has(result.styleDivision)
      )
    }

    if (selectedAgeDivisions.value.size > 0) {
      filtered = filtered.filter(result =>
        result.ageDivision && selectedAgeDivisions.value.has(result.ageDivision)
      )
    }

    if (selectedGenderDivisions.value.size > 0) {
      filtered = filtered.filter(result =>
        result.genderDivision && selectedGenderDivisions.value.has(result.genderDivision)
      )
    }

    // Group by division key
    const groups: Record<string, MatchResult[]> = {}
    filtered.forEach(result => {
      const divisionKey = createDivisionKey(result)
      if (!groups[divisionKey]) {
        groups[divisionKey] = []
      }
      groups[divisionKey].push(result)
    })

    // Sort each group by place
    Object.keys(groups).forEach(key => {
      groups[key].sort((a, b) => {
        const placeA = a.place || 999
        const placeB = b.place || 999
        if (placeA !== placeB) {
          return placeA - placeB
        }
        return (b.points || 0) - (a.points || 0)
      })
    })

    return groups
  }

  // Single division view
  const divisionResults = groupedResults.value[selectedDivision.value] || []
  return { [selectedDivision.value]: divisionResults }
})

// Filtered results for the selected tab (backward compatibility)
const filteredResults = computed(() => {
  if (selectedDivision.value === 'all') {
    // Return all results from all groups
    return Object.values(groupedFilteredResults.value).flat()
  }
  return groupedFilteredResults.value[selectedDivision.value] || []
})

// Filter toggle functions
function toggleStyleDivision(division: string) {
  if (selectedStyleDivisions.value.has(division)) {
    selectedStyleDivisions.value.delete(division)
  } else {
    selectedStyleDivisions.value.add(division)
  }
}

function toggleAgeDivision(division: string) {
  if (selectedAgeDivisions.value.has(division)) {
    selectedAgeDivisions.value.delete(division)
  } else {
    selectedAgeDivisions.value.add(division)
  }
}

function toggleGenderDivision(division: string) {
  if (selectedGenderDivisions.value.has(division)) {
    selectedGenderDivisions.value.delete(division)
  } else {
    selectedGenderDivisions.value.add(division)
  }
}

// Calculate effectiveness percentage
function calculateEffectiveness(points: number, maxPoints: number): number {
  if (maxPoints <= 0) return 0
  return Math.round((points / maxPoints) * 100)
}

// Get medal icon for place
function getMedalIcon(place: number | undefined) {
  if (!place) return null

  switch (place) {
    case 1:
      return { icon: Medal, class: 'text-yellow-500' } // Gold
    case 2:
      return { icon: Medal, class: 'text-gray-400' } // Silver
    case 3:
      return { icon: Medal, class: 'text-amber-600' } // Bronze
    default:
      return null
  }
}

// Check if result has multiple rounds
function hasMultipleRounds(result: MatchResult): boolean {
  return Array.isArray(result.scores) && result.scores.length > 1
}

// Get round scores for display
function getRoundScores(result: MatchResult): number[] {
  if (Array.isArray(result.scores)) {
    return result.scores
  }
  return []
}

// Check if current player is in results
const currentPlayerResult = computed(() => {
  if (!activePlayer.value) return null
  return results.value.find(result => result.playerId === activePlayer.value?.id) || null
})

// Check if current player has a result in the filtered results
const hasCurrentPlayerInResults = computed(() => {
  if (!activePlayer.value || !currentPlayerResult.value) return false
  return filteredResults.value.some(result => result.playerId === activePlayer.value?.id)
})

// Check if result belongs to current player
function isCurrentPlayerResult(result: MatchResult): boolean {
  if (!activePlayer.value) return false
  return result.playerId === activePlayer.value.id
}

// Scroll to current player's result
function scrollToCurrentPlayer() {
  if (currentPlayerResultRef.value) {
    currentPlayerResultRef.value.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  }
}
</script>

<template>
  <div class="space-y-4 mt-0">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <Trophy class="h-5 w-5" />
        <h3 class="text-lg font-semibold">{{ t('matches.results.title') }}</h3>
        <span v-if="results.length > 0" class="text-sm text-muted-foreground">
          ({{ filteredResults.length }}/{{ results.length }})
        </span>
      </div>
    </div>

    <div
      v-if="hasCurrentPlayerInResults && !isLoadingResults && !resultsError && isMatchInPast()"
      @click="scrollToCurrentPlayer"
      class="cursor-pointer transition-opacity hover:opacity-80"
      :title="t('matches.results.scrollToYourResult')"
    >
      <PlayerResultWidget :match="match" />
    </div>
    <PlayerResultWidget v-else :match="match" />

    <div v-if="isLoadingResults" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
      <p class="text-muted-foreground">{{ t('matches.results.loadingResults') }}</p>
    </div>

    <div v-else-if="resultsError" class="text-center py-8 text-destructive">
      <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
      <p>{{ t('matches.results.failedToLoad') }}</p>
      <p class="text-sm">{{ resultsError.message }}</p>
    </div>

    <div v-else-if="isMatchInPast()">
      <div v-if="results.length > 0">
        <!-- Division Selector -->
        <div class="mb-4">
          <Select v-model="selectedDivision">
            <SelectTrigger class="w-64">
              <SelectValue :placeholder="t('matches.results.selectDivision')" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="division in availableDivisions"
                :key="division"
                :value="division"
              >
                <span class="flex items-center gap-2">
                  {{
                    division === 'all'
                      ? t('matches.results.allDivisions')
                      : division || t('common.na')
                  }}
                  <Badge
                    v-if="division !== 'all'"
                    variant="secondary"
                    class="text-xs"
                  >
                    {{ groupedResults[division]?.length || 0 }}
                  </Badge>
                </span>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Filters for "All Divisions" -->
        <div v-if="selectedDivision === 'all'" class="space-y-3 mb-4">
          <div v-if="availableStyleDivisions.length > 0">
            <h4 class="text-sm font-medium mb-2">{{ t('matches.styleDivisions') }}</h4>
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="styleDivision in availableStyleDivisions"
                :key="styleDivision"
                :variant="selectedStyleDivisions.has(styleDivision) ? 'default' : 'outline'"
                size="sm"
                @click="toggleStyleDivision(styleDivision)"
              >
                {{ styleDivision }}
              </Button>
            </div>
          </div>

          <div v-if="availableAgeDivisions.length > 0">
            <h4 class="text-sm font-medium mb-2">{{ t('matches.ageDivisions') }}</h4>
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="ageDivision in availableAgeDivisions"
                :key="ageDivision"
                :variant="selectedAgeDivisions.has(ageDivision) ? 'default' : 'outline'"
                size="sm"
                @click="toggleAgeDivision(ageDivision)"
              >
                {{ ageDivision }}
              </Button>
            </div>
          </div>

          <div v-if="availableGenderDivisions.length > 0">
            <h4 class="text-sm font-medium mb-2">{{ t('matches.results.genderDivisions') }}</h4>
            <div class="flex flex-wrap gap-2">
              <Button
                v-for="genderDivision in availableGenderDivisions"
                :key="genderDivision"
                :variant="selectedGenderDivisions.has(genderDivision) ? 'default' : 'outline'"
                size="sm"
                @click="toggleGenderDivision(genderDivision)"
              >
                {{ genderDivision }}
              </Button>
            </div>
          </div>
        </div>

        <!-- Results Display -->
        <div v-if="selectedDivision === 'all'">
          <!-- Grouped view for "All Divisions" -->
          <div v-if="Object.keys(groupedFilteredResults).length > 0" class="space-y-6">
            <div
              v-for="(divisionResults, divisionKey) in groupedFilteredResults"
              :key="divisionKey"
              class="space-y-3"
            >
              <!-- Division Header -->
              <div class="flex items-center gap-2 border-b pb-2">
                <h4 class="text-lg font-semibold">{{ divisionKey || t('common.na') }}</h4>
                <Badge variant="outline" class="text-xs">
                  {{ divisionResults.length }} {{ divisionResults.length === 1 ? 'participant' : 'participants' }}
                </Badge>
              </div>

              <!-- Division Results -->
              <div class="space-y-2">
                <div
                  v-for="result in divisionResults"
                  :key="result.id"
                  :ref="isCurrentPlayerResult(result) ? (el) => currentPlayerResultRef = el as HTMLElement : undefined"
                  :class="[
                    'flex items-center justify-between p-4 border rounded-lg',
                    isCurrentPlayerResult(result)
                      ? 'bg-primary/10 border-primary/30 ring-2 ring-primary/20'
                      : 'bg-card'
                  ]"
                >
                  <div class="flex items-center gap-4">
                    <!-- Medal/Place -->
                    <div class="flex items-center justify-center w-8 h-8">
                      <component
                        v-if="getMedalIcon(result.place)"
                        :is="getMedalIcon(result.place)!.icon"
                        :class="['h-6 w-6', getMedalIcon(result.place)!.class]"
                      />
                      <Badge
                        v-else-if="result.place"
                        variant="outline"
                        class="text-xs"
                      >
                        {{ result.place }}
                      </Badge>
                    </div>

                    <!-- Player Info -->
                    <div>
                      <p class="font-medium">
                        {{ result.player?.firstname }} {{ result.player?.lastname }}
                      </p>
                      <div class="flex items-center gap-1 text-sm text-muted-foreground">
                        <img
                          v-if="(result.player as any)?.country"
                          :src="`/flags/${(result.player as any)?.country?.toLowerCase()}.svg`"
                          :alt="`${(result.player as any)?.country} flag`"
                          class="w-4 h-3 object-cover"
                          @error="($event.target as HTMLImageElement).style.display = 'none'"
                        />
                        <span>{{ (result.player as any)?.club || t('matches.results.noClub') }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Score Info -->
                  <div class="text-right">
                    <!-- Main Score -->
                    <div class="space-y-2">
                      <div class="flex items-center gap-2 justify-end">
                        <p class="font-bold">{{ result.points }}/{{ result.maxPoints }}</p>
                        <span class="text-sm text-muted-foreground">
                          ({{ calculateEffectiveness(result.points || 0, result.maxPoints || 0) }}%)
                        </span>
                      </div>
                      <!-- Effectiveness Gauge -->
                      <div class="w-32 ml-auto">
                        <EffectivenessGauge
                          :value="calculateEffectiveness(result.points || 0, result.maxPoints || 0)"
                          :show-value="false"
                          label=""
                        />
                      </div>
                    </div>

                    <!-- Round Breakdown -->
                    <div v-if="hasMultipleRounds(result)" class="mt-2">
                      <div class="flex gap-1 text-xs text-muted-foreground">
                        <span
                          v-for="(score, roundIndex) in getRoundScores(result)"
                          :key="roundIndex"
                          class="px-2 py-1 bg-muted rounded"
                        >
                          {{ t('matches.results.round') }} {{ roundIndex + 1 }}: {{ score }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="text-center py-8 text-muted-foreground">
            <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>{{ t('matches.results.noResultsMatchFilter') }}</p>
            <p class="text-sm">{{ t('matches.results.adjustFilters') }}</p>
          </div>
        </div>

        <!-- Single division view -->
        <div v-else>
          <div v-if="filteredResults.length > 0" class="space-y-3">
            <div
              v-for="result in filteredResults"
              :key="result.id"
              :ref="isCurrentPlayerResult(result) ? (el) => currentPlayerResultRef = el as HTMLElement : undefined"
              :class="[
                'flex items-center justify-between p-4 border rounded-lg',
                isCurrentPlayerResult(result)
                  ? 'bg-primary/10 border-primary/30 ring-2 ring-primary/20'
                  : 'bg-card'
              ]"
            >
              <div class="flex items-center gap-4">
                <!-- Medal/Place -->
                <div class="flex items-center justify-center w-8 h-8">
                  <component
                    v-if="getMedalIcon(result.place)"
                    :is="getMedalIcon(result.place)!.icon"
                    :class="['h-6 w-6', getMedalIcon(result.place)!.class]"
                  />
                  <Badge
                    v-else-if="result.place"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ result.place }}
                  </Badge>
                </div>

                <!-- Player Info -->
                <div>
                  <p class="font-medium">
                    {{ result.player?.firstname }} {{ result.player?.lastname }}
                  </p>
                  <div class="flex items-center gap-1 text-sm text-muted-foreground">
                    <img
                      v-if="(result.player as any)?.country"
                      :src="`/flags/${(result.player as any)?.country?.toLowerCase()}.svg`"
                      :alt="`${(result.player as any)?.country} flag`"
                      class="w-4 h-3 object-cover"
                      @error="($event.target as HTMLImageElement).style.display = 'none'"
                    />
                    <span>{{ (result.player as any)?.club || t('matches.results.noClub') }}</span>
                  </div>
                </div>
              </div>

              <!-- Score Info -->
              <div class="text-right">
                <!-- Main Score -->
                <div class="space-y-2">
                  <div class="flex items-center gap-2 justify-end">
                    <p class="font-bold">{{ result.points }}/{{ result.maxPoints }}</p>
                    <span class="text-sm text-muted-foreground">
                      ({{ calculateEffectiveness(result.points || 0, result.maxPoints || 0) }}%)
                    </span>
                  </div>
                  <!-- Effectiveness Gauge -->
                  <div class="w-32 ml-auto">
                    <EffectivenessGauge
                      :value="calculateEffectiveness(result.points || 0, result.maxPoints || 0)"
                      :show-value="false"
                      label=""
                    />
                  </div>
                </div>

                <!-- Round Breakdown -->
                <div v-if="hasMultipleRounds(result)" class="mt-2">
                  <div class="flex gap-1 text-xs text-muted-foreground">
                    <span
                      v-for="(score, roundIndex) in getRoundScores(result)"
                      :key="roundIndex"
                      class="px-2 py-1 bg-muted rounded"
                    >
                      {{ t('matches.results.round') }} {{ roundIndex + 1 }}: {{ score }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="text-center py-8 text-muted-foreground">
            <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>{{ t('matches.results.noResultsMatchFilter') }}</p>
            <p class="text-sm">{{ t('matches.results.adjustFilters') }}</p>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8 text-muted-foreground">
        <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
        <p>{{ t('matches.results.noResultsYet') }}</p>
        <p class="text-sm">{{ t('matches.results.resultsAfterScoring') }}</p>
      </div>
    </div>

    <div v-else class="text-center py-8 text-muted-foreground">
      <Trophy class="h-12 w-12 mx-auto mb-2 opacity-50" />
      <p>{{ t('matches.results.matchNotFinished') }}</p>
      <p class="text-sm">{{ t('matches.results.scoresAfterMatch') }}</p>
    </div>
  </div>
</template>
