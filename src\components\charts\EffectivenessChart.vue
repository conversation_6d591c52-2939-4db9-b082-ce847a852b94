<script setup lang="ts">
import { VisXYContainer, VisLine, VisAxis } from '@unovis/vue'

interface DataRecord {
  x: Date
  y: number
}

const { data } = defineProps<{ data: DataRecord[] }>()
const x = (d: DataRecord) => d.x
const y = (d: DataRecord) => d.y

// Format x-axis to show month-year
const xFormatter = (tick: number | Date) => {
  const date = tick instanceof Date ? tick : new Date(tick)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric'
  })
}
</script>

<template>
  <VisXYContainer :data="data">
    <VisLine :x="x" :y="y" />
    <VisAxis type="x" :tickFormat="xFormatter" :gridLine="false" />
    <VisAxis type="y" :gridLine="false" />
  </VisXYContainer>
</template>
