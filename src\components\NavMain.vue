<script setup lang="ts">
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { type LucideIcon } from 'lucide-vue-next'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps<{
  matchesItems: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
  }[]
  otherItems: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
  }[]
  organizerItems?: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
  }[]
  showOrganizerMenu?: boolean
}>()

const route = useRoute()
</script>

<template>
  <!-- Organizer Menu - shown when activeOrganizer is set -->
  <SidebarGroup v-if="showOrganizerMenu && organizerItems">
    <SidebarGroupLabel>{{ t('navigation.organizer') }}</SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in organizerItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>

  <!-- Regular Matches Menu - shown when no organizer or always -->
  <SidebarGroup v-if="!showOrganizerMenu">
    <SidebarGroupLabel>{{ t('navigation.matches') }}</SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in matchesItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>

  <SidebarGroup v-if="!showOrganizerMenu">
    <SidebarMenu>
      <SidebarMenuItem v-for="item in otherItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
