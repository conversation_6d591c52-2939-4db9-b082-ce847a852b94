import type { Application, Id as FeathersId, NullableId, Paginated, Params } from '@feathersjs/feathers';

import type { Equipment } from '../equipment/equipment.schema';
import type { User } from '../users/users.schema';

// Whitelisted fields for the main user profile
interface UserProfile {
  id: number;
  email: string;
  avatar?: string; // Assuming avatar is whitelisted and optional
  googleId?: string; // Assuming googleId is whitelisted and optional
  facebookId?: string; // Assuming facebookId is whitelisted and optional
  githubId?: string; // Assuming githubId is whitelisted and optional
  // Add other specific fields from User schema you want to expose
  players?: WhitelistedRelatedItem[]; // Define WhitelistedRelatedItem or use specific types
  organizers?: WhitelistedRelatedItem[];
  // equipment moved to be nested within players
}

// Whitelisted fields for related items (e.g., players, organizers)
// Adjust this based on what you want to expose from players/organizers
interface WhitelistedRelatedItem {
  id: number; // Or appropriate Id type
  firstname?: string;
  lastname?: string;
  name?: string;
  address?: string;
  zipcode?: string;
  city?: string;
  country?: string;
  phone?: string;
  birthdate?: string;
  sex?: string;
  latitude?: number;
  longitude?: number;
  avatar?: string;
  licenses?: any[];
  equipment?: Equipment[]; // Add equipment array to players
}

// Whitelisted user fields for the PATCH response. Could be same as UserProfile or different.
// For simplicity, let's align it closely with UserProfile but without relations.
type WhitelistedUserPatchResponse = Omit<UserProfile, 'players' | 'organizers' | 'equipment'>;

export interface UserMeServiceMethods {
  find(params?: Params): Promise<UserProfile>;
  patch(id: 'me', data: Partial<User>, params?: Params): Promise<Partial<WhitelistedUserPatchResponse>>;
}

export class UserMeService implements UserMeServiceMethods {
  constructor(public app: Application) { }

  // Whitelists fields for the main user object for the GET /users/me response
  private pickUserProfileFields(user: User): UserProfile {
    return {
      id: user.id,
      email: user.email,
      avatar: user.avatar,
      googleId: user.googleId,
      facebookId: user.facebookId,
      githubId: user.githubId,
      // Add any other whitelisted fields from the User object here
    };
  }

  // Whitelists fields for the user object for the PATCH /users/me response
  private pickUserPatchResponseFields(user: User): Partial<WhitelistedUserPatchResponse> {
    // This could be the same as pickUserProfileFields if UserProfile doesn't have relations
    // or a subset if you want to return fewer fields on patch.
    return {
      id: user.id,
      email: user.email,
      avatar: user.avatar,
      googleId: user.googleId,
      facebookId: user.facebookId,
      githubId: user.githubId,
      // Add any other whitelisted fields for patch response
    };
  }

  // Whitelists fields for related data (players, organizers)
  private async pickRelatedItemFieldsWithEquipment(item: any, params?: Params): Promise<WhitelistedRelatedItem> {
    // Get equipment for this player
    const equipmentService = this.app.service('equipment');
    const equipmentParams = { ...params, query: { ...(params?.query || {}), playerId: item.id }, provider: undefined };
    const equipmentResults = await equipmentService.find(equipmentParams);
    const equipmentData = Array.isArray(equipmentResults) ? equipmentResults : (equipmentResults as Paginated<any>).data;

    return {
      id: item.id,
      firstname: item.firstname,
      lastname: item.lastname,
      address: item.address,
      zipcode: item.zipcode,
      city: item.city,
      country: item.country,
      phone: item.phone,
      birthdate: item.birthdate,
      sex: item.sex,
      latitude: item.latitude,
      longitude: item.longitude,
      avatar: item.avatar,
      licenses: item.licenses,
      equipment: equipmentData
    };
  }

  // Whitelists fields for related data (organizers - without equipment)
  private pickRelatedItemFields(item: any): WhitelistedRelatedItem {
    return {
      id: item.id,
      name: item.name,
      firstname: item.firstname,
      lastname: item.lastname,
      address: item.address,
      zipcode: item.zipcode,
      city: item.city,
      country: item.country,
      phone: item.phone,
      birthdate: item.birthdate,
      sex: item.sex,
      latitude: item.latitude,
      longitude: item.longitude,
      avatar: item.avatar,
      licenses: item.licenses
    };
  }

  async find(params?: Params): Promise<UserProfile> {
    if (!params?.user) {
      throw new Error('User not authenticated');
    }
    const userId = (params.user as any).id;
    if (!userId) {
      throw new Error('Authenticated user ID not found');
    }

    const userService = this.app.service('users');
    const user = await userService.get(userId, {
      ...params,
      provider: undefined
    }) as User;

    const playersService = this.app.service('players');
    const playersParams = { ...params, query: { ...(params?.query || {}), userId }, provider: undefined };
    const playersResults = await playersService.find(playersParams);
    const playersData = Array.isArray(playersResults) ? playersResults : (playersResults as Paginated<any>).data;

    // Get players with their equipment nested
    const whitelistedPlayers = await Promise.all(
      playersData.map(p => this.pickRelatedItemFieldsWithEquipment(p, params))
    );

    const organizersService = this.app.service('organizers');
    const organizersParams = { ...params, query: { ...(params?.query || {}), userId }, provider: undefined };
    const organizersResults = await organizersService.find(organizersParams);
    const organizersData = Array.isArray(organizersResults) ? organizersResults : (organizersResults as Paginated<any>).data;
    const whitelistedOrganizers = organizersData.map(o => this.pickRelatedItemFields(o));

    const userProfileData = this.pickUserProfileFields(user);

    return {
      ...userProfileData,
      players: whitelistedPlayers,
      organizers: whitelistedOrganizers
    };
  }

  async patch(id: 'me', data: Partial<User>, params?: Params): Promise<Partial<WhitelistedUserPatchResponse>> {
    if (!params?.user) {
      throw new Error('User not authenticated');
    }
    const userId = (params.user as any).id;
    if (!userId) {
      throw new Error('Authenticated user ID not found');
    }

    const userService = this.app.service('users');
    const updatedUser = await userService.patch(userId, data, {
      ...params,
      provider: undefined
    }) as unknown as User;

    return this.pickUserPatchResponseFields(updatedUser);
  }

  // Explicitly define other standard service methods as not implemented for this specific service
  async get(id: NullableId, params?: Params): Promise<User | User[]> {
    throw new Error('Method not implemented for /users/me. Use find.');
  }
  async create(data: Partial<User> | Partial<User>[], params?: Params): Promise<User | User[]> {
    throw new Error('Method not implemented for /users/me.');
  }
  async update(id: NullableId, data: User, params?: Params): Promise<User | User[]> {
    throw new Error('Method not implemented for /users/me.');
  }
  async remove(id: NullableId, params?: Params): Promise<User | User[]> {
    throw new Error('Method not implemented for /users/me.');
  }
}
