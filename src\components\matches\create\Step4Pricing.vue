<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import type { AgeDivision } from '@/stores/federations'

const { t } = useI18n()

const props = defineProps<{
  payments: Record<string, number>
  currency?: string
  ageDivisions: AgeDivision[] | string[]
  isFederationBased: boolean
}>()

const emit = defineEmits<{
  (e: 'update:payments', value: Record<string, number>): void
  (e: 'update:currency', value: string): void
}>()

const validAgeDivisions = computed(() => {
  return (props.ageDivisions as AgeDivision[]).filter(
    (div) => typeof div === 'object' && 'short_name' in div && div.short_name
  )
})

const ageDivisionShortNames = computed(() => {
  return validAgeDivisions.value.map(div => div.short_name)
})

function updatePayment(shortName: string, value: string | number) {
  const newPayments = { ...props.payments }
  newPayments[shortName] = typeof value === 'string' ? parseFloat(value) || 0 : value || 0;
  emit('update:payments', newPayments)
}
</script>

<template>
  <div class="space-y-6">
    <div v-if="ageDivisionShortNames.length === 0" class="text-center py-8">
      <p class="text-muted-foreground">{{ t('matches.noPricingAvailable') }}</p>
      <p class="text-sm text-muted-foreground mt-2">{{ t('matches.addAgeDivisionsFirst') }}</p>
    </div>

    <div v-else class="space-y-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-lg font-semibold mb-2">{{ t('matches.pricingByAgeDivision') }}</h3>
          <p class="text-sm text-muted-foreground">{{ t('matches.pricingDescription') }}</p>
        </div>
        <div class="flex items-center gap-2">
          <Label for="currency" class="text-sm font-medium">{{ t('matches.currency') }}</Label>
          <Select
            :model-value="currency"
            @update:model-value="emit('update:currency', $event as string)"
          >
            <SelectTrigger class="w-24">
              <SelectValue placeholder="Currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PLN">PLN</SelectItem>
              <SelectItem value="EUR">EUR</SelectItem>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="CZK">CZK</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="flex flex-col gap-4 w-full md:w-1/2">
        <div
          v-for="div in validAgeDivisions"
          :key="div.short_name"
          class="p-2 border rounded-lg flex flex-row items-center justify-between gap-4"
        >
          <div class="flex flex-row items-center gap-2">
            <Label class="font-medium min-w-[100px] text-left" :for="`price-${div.short_name}`">
              {{ div.name || div.short_name }}
            </Label>
            <Badge variant="secondary" class="ml-2">{{ div.short_name }}</Badge>
          </div>
          <div class="flex flex-row items-center gap-2">
            <Input
              :id="`price-${div.short_name}`"
              :model-value="payments[div.short_name]"
              @update:model-value="updatePayment(div.short_name, $event)"
              type="number"
              min="0"
              step="0.01"
              :placeholder="t('matches.enterPrice')"
              class="text-right w-24"
            />
            <Badge variant="outline" class="ml-2">{{ currency || 'PLN' }}</Badge>
          </div>
        </div>
      </div>

      <!-- Summary -->
      <div class="mt-6 p-4 bg-muted/30 rounded-lg">
        <h4 class="font-medium mb-2">{{ t('matches.pricingSummary') }}</h4>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 text-sm">
          <div
            v-for="shortName in ageDivisionShortNames"
            :key="`summary-${shortName}`"
            class="flex justify-between"
          >
            <span>{{ shortName }}:</span>
            <span class="font-medium">{{ payments[shortName] || 0 }} {{ currency || 'PLN' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
